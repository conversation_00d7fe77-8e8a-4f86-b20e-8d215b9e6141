# 환경변수 설정 예시 파일
# 실제 사용시 .env 파일로 복사하고 실제 API 키를 입력하세요

# ===========================================
# 업비트 API 키 (실제 거래용) - 필수
# ===========================================
UPBIT_ACCESS_KEY=your_upbit_access_key_here
UPBIT_SECRET_KEY=your_upbit_secret_key_here

# ===========================================
# Bybit API 키 (데이터 수집용) - 선택사항
# ===========================================
# 참고: Bybit 공개 데이터는 API 키 없이도 접근 가능
BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_API_SECRET=your_bybit_api_secret_here

# ===========================================
# 거래 설정
# ===========================================
LIVE_TRADING_ENABLED=false
TRADE_QUANTITY_USDT=10
TRADE_INTERVAL_SECONDS=60
MAX_INVESTMENT_PER_COIN=100000
ORDER_AMOUNT=15000

# ===========================================
# 로깅 설정
# ===========================================
LOG_LEVEL=INFO
