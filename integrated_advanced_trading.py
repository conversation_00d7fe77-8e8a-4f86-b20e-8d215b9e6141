"""
통합 고급 트레이딩 시스템
- main5의 SMC, VPVR 고급 분석
- main6의 시각화 및 속도
- 업비트 실제 매매 연동
"""
import pandas as pd
import numpy as np
import pyupbit
import os
import time
from datetime import datetime
from dotenv import load_dotenv
import warnings
warnings.filterwarnings('ignore')

# 고급 분석기 임포트
from advanced_analysis_with_visualization import AdvancedCryptoAnalyzer

# 환경변수 로드
load_dotenv()


class IntegratedAdvancedTrading:
    """통합 고급 트레이딩 시스템"""
    
    def __init__(self, live_trading=False):
        self.live_trading = live_trading
        
        # 심볼 매핑 (Bybit -> 업비트)
        self.symbol_mapping = {
            'BTCUSDT': 'KRW-BTC',
            'ETHUSDT': 'KRW-ETH', 
            'SOLUSDT': 'KRW-SOL',
            'XRPUSDT': 'KRW-XRP'
        }
        
        # 업비트 API 초기화
        if live_trading:
            access_key = os.getenv("UPBIT_ACCESS_KEY")
            secret_key = os.getenv("UPBIT_SECRET_KEY")
            if not access_key or not secret_key:
                print("❌ 업비트 API 키가 설정되지 않았습니다!")
                print("💡 .env 파일에 API 키를 설정하거나 시뮬레이션 모드를 사용하세요.")
                raise ValueError("업비트 API 키가 설정되지 않았습니다!")
            self.upbit = pyupbit.Upbit(access_key, secret_key)
        else:
            self.upbit = None
            print("⚠️  시뮬레이션 모드 - 실제 거래하지 않습니다")
        
        # 고급 분석기 초기화
        self.analyzer = AdvancedCryptoAnalyzer()
        
        # 거래 설정
        self.max_investment_per_coin = 100000  # 종목당 최대 10만원
        self.order_amount = 25000  # 1회 주문 금액 2.5만원
        self.min_order_amount = 5000  # 최소 주문 금액
        
        # 신호 임계값 설정
        self.buy_threshold = 20   # 매수 신호 임계값
        self.sell_threshold = -20  # 매도 신호 임계값
        self.strong_buy_threshold = 40   # 강매수 임계값
        self.strong_sell_threshold = -40  # 강매도 임계값
        
        # 상태 추적
        self.last_signals = {}
        self.last_analysis_time = {}
        
        print(f"🚀 통합 고급 트레이딩 시스템 초기화")
        print(f"   - 실제 거래: {'ON' if live_trading else 'OFF'}")
        print(f"   - 분석 방식: SMC + VPVR + 거래량 분석")
        print(f"   - 대상 종목: {list(self.symbol_mapping.keys())}")
    
    def convert_signal_to_action(self, strategy_result):
        """분석 결과를 매매 액션으로 변환"""
        score = strategy_result['score']
        final_signal = strategy_result['final_signal']
        
        # 점수 기반 액션 결정
        if score >= self.strong_buy_threshold:
            return "StrongBuy", score
        elif score >= self.buy_threshold:
            return "Buy", score
        elif score <= self.strong_sell_threshold:
            return "StrongSell", score
        elif score <= self.sell_threshold:
            return "Sell", score
        else:
            return "Hold", score
    
    def get_upbit_account_info(self):
        """업비트 계좌 정보 조회"""
        if not self.live_trading or not self.upbit:
            return {'krw_balance': 1000000, 'positions': {}}  # 시뮬레이션
        
        try:
            # KRW 잔고
            krw_balance = self.upbit.get_balance("KRW")
            
            # 보유 코인 잔고
            positions = {}
            for upbit_symbol in self.symbol_mapping.values():
                coin_name = upbit_symbol.split("-")[1]
                balance = self.upbit.get_balance(coin_name)
                if balance > 0:
                    current_price = pyupbit.get_current_price(upbit_symbol)
                    positions[upbit_symbol] = {
                        'balance': balance,
                        'current_price': current_price,
                        'value': balance * current_price
                    }
            
            return {
                'krw_balance': krw_balance,
                'positions': positions,
                'total_value': krw_balance + sum(pos['value'] for pos in positions.values())
            }
            
        except Exception as e:
            print(f"❌ 계좌 정보 조회 오류: {e}")
            return {'error': str(e)}
    
    def execute_upbit_trade(self, upbit_symbol, action, score, confidence=None):
        """업비트에서 거래 실행"""
        if not self.live_trading:
            print(f"📝 [시뮬레이션] {upbit_symbol}: {action} (점수: {score})")
            return True
        
        try:
            account = self.get_upbit_account_info()
            if 'error' in account:
                return False
            
            coin_name = upbit_symbol.split("-")[1]
            current_price = pyupbit.get_current_price(upbit_symbol)
            
            if action in ['Buy', 'StrongBuy']:
                # 매수 로직
                available_krw = account['krw_balance']
                
                # 종목당 투자 한도 확인
                current_position_value = account['positions'].get(upbit_symbol, {}).get('value', 0)
                remaining_limit = self.max_investment_per_coin - current_position_value
                
                # 강매수 신호일 때 주문 금액 증가
                order_amount = self.order_amount
                if action == 'StrongBuy':
                    order_amount = min(self.order_amount * 1.5, remaining_limit)
                
                # 실제 주문 금액 계산
                final_order_amount = min(order_amount, available_krw, remaining_limit)
                
                if final_order_amount >= self.min_order_amount:
                    result = self.upbit.buy_market_order(upbit_symbol, final_order_amount)
                    if result:
                        print(f"✅ 매수 완료: {upbit_symbol} {final_order_amount:,}원 (점수: {score})")
                        return True
                    else:
                        print(f"❌ 매수 실패: {upbit_symbol}")
                        return False
                else:
                    print(f"⚠️  매수 스킵: {upbit_symbol} (주문금액 부족: {final_order_amount:,}원)")
                    return False
            
            elif action in ['Sell', 'StrongSell']:
                # 매도 로직
                coin_balance = self.upbit.get_balance(coin_name)
                
                if coin_balance > 0:
                    # 강매도 신호일 때 전량 매도, 일반 매도일 때 50% 매도
                    sell_ratio = 1.0 if action == 'StrongSell' else 0.5
                    sell_amount = coin_balance * sell_ratio
                    
                    result = self.upbit.sell_market_order(upbit_symbol, sell_amount)
                    if result:
                        print(f"✅ 매도 완료: {upbit_symbol} {sell_amount:.8f}개 ({sell_ratio*100:.0f}%, 점수: {score})")
                        return True
                    else:
                        print(f"❌ 매도 실패: {upbit_symbol}")
                        return False
                else:
                    print(f"⚠️  매도 스킵: {upbit_symbol} (보유량 없음)")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ {upbit_symbol} 거래 실행 오류: {e}")
            return False
    
    def analyze_and_trade_symbol(self, bybit_symbol):
        """심볼 분석 및 거래"""
        upbit_symbol = self.symbol_mapping[bybit_symbol]
        
        print(f"\n📊 {bybit_symbol} 고급 분석 시작")
        print(f"   → {upbit_symbol} 거래 대상")
        
        try:
            # 고급 분석 실행
            results = self.analyzer.analyze_symbol(bybit_symbol)
            
            if not results or '20일' not in results:
                print(f"❌ {bybit_symbol} 분석 실패")
                return None
            
            # 20일 기준 분석 결과 사용
            analysis_result = results['20일']
            strategy = analysis_result['strategy']
            
            # 매매 액션 결정
            action, score = self.convert_signal_to_action(strategy)
            
            print(f"   🎯 분석 결과: {strategy['final_signal']} (점수: {score})")
            print(f"   🔄 매매 액션: {action}")
            
            # 신호 변화 확인 (같은 신호 반복 방지)
            last_signal = self.last_signals.get(bybit_symbol)
            if last_signal != action:
                if action in ['Buy', 'StrongBuy', 'Sell', 'StrongSell']:
                    success = self.execute_upbit_trade(upbit_symbol, action, score)
                    if success:
                        self.last_signals[bybit_symbol] = action
                        self.last_analysis_time[bybit_symbol] = datetime.now()
                else:
                    print(f"⏸️  {upbit_symbol}: 보류 ({action})")
            else:
                print(f"🔄 {bybit_symbol}: 신호 변화 없음 ({action})")
            
            return {
                'symbol': bybit_symbol,
                'upbit_symbol': upbit_symbol,
                'action': action,
                'score': score,
                'strategy': strategy,
                'analysis': analysis_result['analysis']
            }
            
        except Exception as e:
            print(f"❌ {bybit_symbol} 분석 오류: {e}")
            return None
    
    def run_analysis_cycle(self):
        """분석 사이클 실행"""
        print(f"\n{'='*80}")
        print(f"🔍 고급 분석 + 업비트 매매 사이클: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 SMC + VPVR + 거래량 분석 → 업비트 거래")
        print(f"{'='*80}")
        
        # 업비트 계좌 정보 출력
        account = self.get_upbit_account_info()
        if 'error' not in account:
            print(f"💰 업비트 KRW 잔고: {account['krw_balance']:,.0f}원")
            if account['positions']:
                print("📊 업비트 보유 포지션:")
                for symbol, pos in account['positions'].items():
                    print(f"   {symbol}: {pos['balance']:.6f}개 ({pos['value']:,.0f}원)")
        
        # 각 심볼 분석 및 거래
        cycle_results = {}
        
        for bybit_symbol in self.symbol_mapping.keys():
            try:
                result = self.analyze_and_trade_symbol(bybit_symbol)
                if result:
                    cycle_results[bybit_symbol] = result
                time.sleep(2)  # API 제한 고려
            except Exception as e:
                print(f"❌ {bybit_symbol} 처리 오류: {e}")
        
        # 사이클 요약
        print(f"\n📋 사이클 요약:")
        for symbol, result in cycle_results.items():
            print(f"  {symbol}: {result['action']} (점수: {result['score']})")
        
        return cycle_results
    
    def run_continuous(self, interval_minutes=10):
        """연속 실행"""
        print(f"🔄 연속 실행 시작 (간격: {interval_minutes}분)")
        print("📊 고급 분석 → 업비트 거래 자동화")
        print("Ctrl+C로 중지할 수 있습니다.")
        
        try:
            while True:
                self.run_analysis_cycle()
                
                print(f"\n⏰ {interval_minutes}분 대기 중...")
                time.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            print("\n🛑 사용자에 의해 중지되었습니다.")
        except Exception as e:
            print(f"\n❌ 시스템 오류: {e}")
    
    def run_single_analysis(self):
        """단일 분석 실행"""
        return self.run_analysis_cycle()


def main():
    """메인 실행 함수"""
    print("🚀 통합 고급 트레이딩 시스템")
    print("=" * 60)
    print("📊 분석: Bybit (SMC + VPVR + 고급 거래량 분석)")
    print("💰 매매: 업비트 (KRW-BTC, KRW-ETH, KRW-SOL, KRW-XRP)")
    print("🎯 신호: 점수 기반 매매 결정")
    
    # 실행 모드 선택
    print("\n실행 모드를 선택하세요:")
    print("1. 시뮬레이션 모드 (안전)")
    print("2. 실제 거래 모드 (주의!)")
    print("3. 분석만 실행 (차트 생성)")
    
    try:
        choice = input("선택 (1, 2, 또는 3): ").strip()
        
        if choice == "3":
            # 분석만 실행
            analyzer = AdvancedCryptoAnalyzer()
            analyzer.run_analysis()
            return
        
        elif choice == "1":
            live_trading = False
            print("✅ 시뮬레이션 모드 선택")
        elif choice == "2":
            live_trading = True
            confirm = input("⚠️  실제 거래 모드입니다. 정말 진행하시겠습니까? (yes/no): ").strip().lower()
            if confirm != "yes":
                print("❌ 취소되었습니다.")
                return
            print("⚠️  실제 거래 모드 선택")
        else:
            print("❌ 잘못된 선택입니다.")
            return
        
        # 시스템 초기화
        system = IntegratedAdvancedTrading(live_trading=live_trading)
        
        # 실행 방식 선택
        print("\n실행 방식을 선택하세요:")
        print("1. 단일 분석")
        print("2. 연속 실행 (10분 간격)")
        
        run_choice = input("선택 (1 또는 2): ").strip()
        
        if run_choice == "1":
            system.run_single_analysis()
        elif run_choice == "2":
            system.run_continuous(10)
        else:
            print("❌ 잘못된 선택입니다.")
            
    except Exception as e:
        print(f"❌ 시스템 오류: {e}")


if __name__ == "__main__":
    main()
