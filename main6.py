import pandas as pd
import numpy as np
import requests
import time
import os
import matplotlib.pyplot as plt
from scipy.stats import linregress
import warnings
from config import *  # 설정 파일 임포트

# 경고 무시 설정
warnings.filterwarnings("ignore", category=FutureWarning)

# 디렉토리 생성 함수
def create_directory(path):
    if not os.path.exists(path):
        os.makedirs(path)

# 1. 데이터 수집 함수
def fetch_bybit_klines(symbol, interval=INTERVAL, years=YEARS_OF_DATA):
    """Bybit API를 사용해 데이터 수집"""
    url = "https://api.bybit.com/v5/market/kline"
    data_list = []
    limit = years * 365 * 24  # 1년치 60분봉 데이터 포인트 수
    
    end_time = int(time.time() * 1000)
    start_time = end_time - (years * 365 * 24 * 60 * 60 * 1000)
    
    while start_time < end_time:
        params = {
            "category": "linear",
            "symbol": symbol,
            "interval": interval,
            "start": start_time,
            "end": end_time,
            "limit": min(limit, 1000)
        }
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            if data['retCode'] == 0:
                chunk = data['result']['list']
                if not chunk:
                    break
                data_list.extend(chunk)
                end_time = int(chunk[-1][0]) - 1
            else:
                print(f"Error: {data['retMsg']}")
                break
        else:
            print(f"HTTP Error: {response.status_code}")
            break
        
        time.sleep(0.1)
        limit -= len(chunk)
        if limit <= 0:
            break
    
    if not data_list:
        return pd.DataFrame()
    
    df = pd.DataFrame(data_list, columns=[
        'timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'
    ])
    
    df = df[::-1].reset_index(drop=True)
    df['timestamp'] = pd.to_datetime(df['timestamp'].astype('int64'), unit='ms')
    numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'turnover']
    df[numeric_cols] = df[numeric_cols].astype(float)
    df['symbol'] = symbol
    
    return df

# 2. 기술적 지표 계산 함수
def calculate_technical_indicators(df):
    """모든 기술적 지표 계산"""
    # 기본 지표
    df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
    
    # 볼린저 밴드 추가 (변동성 지표)
    window = BOLLINGER_SETTINGS['window']
    std_dev = BOLLINGER_SETTINGS['std_dev']
    df['ma20'] = df['close'].rolling(window).mean()
    df['std'] = df['close'].rolling(window).std()
    df['upper_band'] = df['ma20'] + (df['std'] * std_dev)
    df['lower_band'] = df['ma20'] - (df['std'] * std_dev)
    
    # 이동평균선 추가
    df['ma50'] = df['close'].rolling(50).mean()
    df['ma100'] = df['close'].rolling(100).mean()
    df['ma200'] = df['close'].rolling(200).mean()
    
    # RSI 추가
    rsi_window = RSI_SETTINGS['window']
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).fillna(0)
    loss = (-delta.where(delta < 0, 0)).fillna(0)
    avg_gain = gain.rolling(rsi_window, min_periods=1).mean()
    avg_loss = loss.rolling(rsi_window, min_periods=1).mean()
    rs = avg_gain / avg_loss.replace(0, np.nan).fillna(0.001)
    df['rsi'] = 100 - (100 / (1 + rs))
    df['rsi'] = df['rsi'].clip(0, 100)
    
    # CVD 추가
    df['delta'] = np.where(df['close'] > df['open'], df['volume'], 
                          np.where(df['close'] < df['open'], -df['volume'], 0))
    df['cvd'] = df['delta'].cumsum()
    
    # OBV 추가
    df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()
    
    return df

# 3. 고가/저가 및 거래량 분석 함수
def calculate_analysis(df, period_days):
    """주어진 기간에 대한 종합 분석"""
    period_data = df.tail(period_days)
    
    # 고가/저가 계산
    high = period_data['high'].max()
    low = period_data['low'].min()
    current_price = df['close'].iloc[-1]
    high_ratio = (current_price - low) / (high - low) * 100 if high != low else 50
    
    # 지표 값
    ma20 = period_data['ma20'].iloc[-1]
    ma50 = period_data['ma50'].iloc[-1]
    ma100 = period_data['ma100'].iloc[-1] if 'ma100' in period_data.columns else None
    ma200 = period_data['ma200'].iloc[-1] if 'ma200' in period_data.columns else None
    rsi = period_data['rsi'].iloc[-1]
    upper_band = period_data['upper_band'].iloc[-1]
    lower_band = period_data['lower_band'].iloc[-1]
    
    # 거래량 집중구간 분석
    vpvr_bins = 10
    price_range = period_data['high'].max() - period_data['low'].min()
    bin_size = price_range / vpvr_bins
    
    if bin_size > 0:
        bins = [period_data['low'].min() + i * bin_size for i in range(vpvr_bins + 1)]
        period_data['price_bin'] = pd.cut(period_data['typical_price'], bins=bins)
        vpvr = period_data.groupby('price_bin')['volume'].sum().reset_index()
        vpvr['price_mid'] = vpvr['price_bin'].apply(lambda x: x.mid)
        top_volumes = vpvr.nlargest(3, 'volume')
        volume_zones = [{'price': row['price_mid'], 'volume': row['volume']} 
                        for _, row in top_volumes.iterrows()]
    else:
        volume_zones = []
    
    return {
        'high': high,
        'low': low,
        'current_price': current_price,
        'high_ratio': high_ratio,
        'ma20': ma20,
        'ma50': ma50,
        'ma100': ma100,
        'ma200': ma200,
        'rsi': rsi,
        'upper_band': upper_band,
        'lower_band': lower_band,
        'volume_zones': volume_zones
    }

# 4. 시각화 함수 (강화된 기능)
def visualize_analysis(df, symbol, period_name):
    """종목별 분석 결과 시각화"""
    plt.figure(figsize=(18, 16))
    
    # 1. 가격 차트
    plt.subplot(4, 1, 1)
    plt.plot(df['timestamp'], df['close'], label='Price', color='black', alpha=0.8)
    plt.plot(df['timestamp'], df['ma20'], label='MA20', color='blue', alpha=0.7)
    plt.plot(df['timestamp'], df['ma50'], label='MA50', color='orange', alpha=0.7)
    plt.plot(df['timestamp'], df['ma200'], label='MA200', color='red', alpha=0.7)
    
    # 볼린저 밴드 시각화
    plt.fill_between(df['timestamp'], df['upper_band'], df['lower_band'], 
                     color='gray', alpha=0.2, label='Bollinger Bands')
    
    # RSI 과매수/과매도 구간 표시
    plt.title(f'{symbol} {period_name} 분석 - 가격 및 볼린저 밴드')
    plt.legend()
    plt.grid(True)
    
    # 2. RSI 및 볼린저 밴드 폭
    plt.subplot(4, 1, 2)
    plt.plot(df['timestamp'], df['rsi'], label='RSI', color='purple')
    plt.axhline(y=70, color='r', linestyle='--', alpha=0.5)
    plt.axhline(y=30, color='g', linestyle='--', alpha=0.5)
    plt.fill_between(df['timestamp'], 70, 100, color='red', alpha=0.1)
    plt.fill_between(df['timestamp'], 0, 30, color='green', alpha=0.1)
    
    # 볼린저 밴드 폭 (변동성 지표)
    df['bb_width'] = (df['upper_band'] - df['lower_band']) / df['ma20']
    plt.plot(df['timestamp'], df['bb_width'] * 100, label='BB Width (%)', color='brown', alpha=0.7)
    
    plt.title('RSI 및 볼린저 밴드 폭 (변동성)')
    plt.legend()
    plt.grid(True)
    
    # 3. CVD (Cumulative Volume Delta)
    plt.subplot(4, 1, 3)
    plt.plot(df['timestamp'], df['cvd'], label='CVD', color='green')
    plt.plot(df['timestamp'], df['cvd'].rolling(5).mean(), label='CVD MA(5)', linestyle='--', color='darkgreen')
    plt.title('Cumulative Volume Delta (CVD)')
    plt.legend()
    plt.grid(True)
    
    # 4. OBV (On Balance Volume)
    plt.subplot(4, 1, 4)
    plt.plot(df['timestamp'], df['obv'], label='OBV', color='blue')
    plt.title('On Balance Volume (OBV)')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    
    # 파일 저장
    filename = f"{symbol}_{period_name.replace(' ', '')}_analysis.png"
    save_path = os.path.join(SAVE_DIR, filename)
    plt.savefig(save_path, dpi=DPI)
    plt.close()
    
    return save_path

# 5. 트레이딩 전략 생성 함수 (강화된 로직)
def generate_trading_strategy(analysis):
    """분석 데이터 기반 트레이딩 전략 생성"""
    strategies = []
    
    # 기본 조건
    high_ratio = analysis['high_ratio']
    rsi = analysis['rsi']
    current_price = analysis['current_price']
    upper_band = analysis['upper_band']
    lower_band = analysis['lower_band']
    
    # 전략 1: 고가 근접 시나리오
    if high_ratio > 80:
        if rsi < 70:
            strategies.append("고가 근접했으나 RSI가 과매수 수준이 아님 (추가 상승 가능성)")
        else:
            strategies.append("고가 근접 + RSI 과매수 - 매수 신호 고려")
            
        if current_price > upper_band:
            strategies.append("볼린저 밴드 상단 돌파 - 추세 강화 가능성")
    
    # 전략 2: 저가 근접 시나리오
    elif high_ratio < 20:
        if rsi > 30:
            strategies.append("저가 근접했으나 RSI가 과매도 수준이 아님 (추가 하락 가능성)")
        else:
            strategies.append("저가 근접 + RSI 과매도 - 매수 신호 고려")
            
        if current_price < lower_band:
            strategies.append("볼린저 밴드 하단 돌파 - 반등 가능성")
    
    # 전략 3: 중립 구간
    else:
        strategies.append("중립 구간 - 관망 권장")
        
        # 변동성 분석
        bb_width = (analysis['upper_band'] - analysis['lower_band']) / analysis['ma20']
        if bb_width > 0.1:  # 변동성 확대
            strategies.append("변동성 확대 - 브레이크아웃 가능성 대비")
    
    # 거래량 집중구간 기반 전략
    if analysis['volume_zones']:
        main_zone = analysis['volume_zones'][0]['price']
        if current_price < main_zone:
            strategies.append(f"주요 거래량 구간({main_zone:.2f}) 위에 위치 - 돌파 시 강한 저항 예상")
        else:
            strategies.append(f"주요 거래량 구간({main_zone:.2f}) 아래에 위치 - 지지로 작용 가능")
    
    return strategies

# 6. 메인 실행 함수
def main():
    create_directory(SAVE_DIR)
    
    for symbol in SYMBOLS:
        print(f"\n{'='*50}")
        print(f"{symbol} 분석 시작")
        print(f"{'='*50}")
        
        # 데이터 수집
        print(f"{symbol} 데이터 수집 중...")
        data = fetch_bybit_klines(symbol=symbol)
        
        if data.empty:
            print(f"{symbol} 데이터 수집 실패. 다음 종목으로 넘어갑니다.")
            continue
        
        # 기술적 지표 계산
        print(f"{symbol} 기술적 지표 계산 중...")
        data = calculate_technical_indicators(data)
        
        # 각 기간별 분석 및 시각화
        for period_name, period_days in PERIODS.items():
            print(f"\n{period_name} 분석 중...")
            
            # 분석 수행
            analysis = calculate_analysis(data, period_days)
            
            # 시각화
            img_path = visualize_analysis(data.tail(period_days*24), symbol, period_name)
            print(f"  시각화 결과 저장: {img_path}")
            
            # 트레이딩 전략 생성
            strategies = generate_trading_strategy(analysis)
            
            # 결과 출력
            print(f"\n[{period_name} 분석 결과]")
            print(f"  고가: {analysis['high']:.2f}")
            print(f"  저가: {analysis['low']:.2f}")
            print(f"  현재가: {analysis['current_price']:.2f}")
            print(f"  고저비율: {analysis['high_ratio']:.2f}%")
            print(f"  RSI: {analysis['rsi']:.2f}")
            print(f"  볼린저 밴드: {analysis['lower_band']:.2f} - {analysis['upper_band']:.2f}")
            
            if analysis['volume_zones']:
                print("\n  📊 거래량 집중구간:")
                for i, zone in enumerate(analysis['volume_zones']):
                    position = "(현재가 위)" if analysis['current_price'] < zone['price'] else "(현재가 아래)"
                    print(f"    {i+1}. {zone['price']:.2f} {position}")
            
            print("\n  🚀 트레이딩 전략:")
            for i, strategy in enumerate(strategies):
                print(f"    {i+1}. {strategy}")
        
        print(f"\n{symbol} 분석 완료")
        print(f"{'='*50}\n")
# main6.py에 추가할 기능
# ...

# 7. 고급 분석 기능
def advanced_analysis(df, symbol):
    """고급 분석 기능 추가"""
    # 7.1 다이버전스 검출
    detect_divergence(df, symbol)
    
    # 7.2 거래량 클러스터 심화 분석
    volume_cluster_analysis(df, symbol)
    
    # 7.3 백테스팅 모듈
    backtest_strategy(df, symbol)
    
    # 7.4 리스크 리포트 생성
    generate_risk_report(df, symbol)

# 7.1 다이버전스 검출
def detect_divergence(df, symbol):
    """가격과 지표 간 다이버전스 검출"""
    # RSI 다이버전스
    # OBV 다이버전스
    # MACD 다이버전스
    print(f"{symbol} - 다이버전스 분석 완료")

# 7.2 거래량 클러스터 심화 분석
def volume_cluster_analysis(df, symbol):
    """고급 거래량 클러스터 분석"""
    threshold = ADVANCED_ANALYSIS['volume_cluster_threshold']
    # 고밀도 거래량 구간 식별
    # 지지/저항 수준 강도 계산
    print(f"{symbol} - 거래량 클러스터 분석 완료")

# 7.3 백테스팅 모듈
def backtest_strategy(df, symbol):
    """전략 백테스팅"""
    period = ADVANCED_ANALYSIS['backtest_period']
    # 과거 데이터 기반 전략 테스트
    # 수익률 계산
    # 위험/수익 비율 분석
    print(f"{symbol} - 백테스팅 완료 (최근 {period}일)")

# 7.4 리스크 리포트 생성
def generate_risk_report(df, symbol):
    """리스크 분석 리포트 생성"""
    # 변동성 분석
    # 드로다운 계산
    # 최대 예상 손실(MDD) 평가
    print(f"{symbol} - 리스크 리포트 생성 완료")        

if __name__ == "__main__":
    main()