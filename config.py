# config.py

# 1. Bybit API Credentials
BYBIT_API_KEY = "A1pXxNuW69izQrrinf"
BYBIT_API_SECRET = "KrDbL4GuM3zl4bttuxvNuxy4HSTFN9XIp4GZ"

# 2. Trading Settings
LIVE_TRADING_ENABLED = False  # 실제 거래 활성화 (True로 설정시 주의!)
TRADE_QUANTITY_USDT = 10  # 거래 금액 (USDT)
TRADE_INTERVAL_SECONDS = 60  # 거래 주기 (초)

# 3. Analysis Settings
# 분석할 종목 리스트
SYMBOLS = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT']

# 데이터 설정
INTERVAL = 60  # 60분봉
YEARS_OF_DATA = 1  # 분석할 데이터 기간 (년)

# 분석 기간 (일 단위)
PERIODS = {
    '5일': 5,
    '20일': 20,
    '50일': 50,
    '100일': 100,
    '200일': 200
}

# 기술적 지표 설정
BOLLINGER_SETTINGS = {
    'window': 20,  # 볼린저 밴드 기간
    'std_dev': 2   # 표준편차 배수
}

RSI_SETTINGS = {
    'window': 14  # RSI 계산 기간
}

# 시각화 설정
SAVE_DIR = './analysis_results/'  # 분석 결과 저장 경로
DPI = 300  # 이미지 해상도

# 4. Risk Management
STOP_LOSS_PERCENT = 2.0  # 손절라인 (%)
TAKE_PROFIT_PERCENT = 5.0  # 익절라인 (%)

# 5. Notification Settings
TELEGRAM_ENABLED = False
TELEGRAM_BOT_TOKEN = "YOUR_TELEGRAM_BOT_TOKEN"
TELEGRAM_CHAT_ID = "YOUR_TELEGRAM_CHAT_ID"

# config.py에 추가할 설정
# ...

# 6. Advanced Analysis Settings
ADVANCED_ANALYSIS = {
    'volume_cluster_threshold': 0.7,  # 거래량 집중구간 임계값 (전체 거래량 대비 %)
    'divergence_window': 14,         # 다이버전스 검출 기간
    'backtest_period': 90,           # 백테스팅 기간 (일)
    'signal_confirmation': True,     # 신호 확인 사용 여부
}

# config.py

# 7. Backtesting & Optimization Settings
BACKTEST_SETTINGS = {
    'initial_capital': 10000.0,  # 초기 자본금 (USDT)
    'commission_rate': 0.0004,   # 거래 수수료 (0.04%)
    'slippage_rate': 0.0005      # 슬리피지 (0.05%)
}

# 8. Machine Learning Settings (신규 추가)
ML_SETTINGS = {
    'enabled': False,
    'model_path': './models/',
    'retrain_interval': 30  # 모델 재학습 주기 (일)
}

# 9. Sentiment Analysis (신규 추가)
SENTIMENT_SETTINGS = {
    'news_api_key': "YOUR_NEWS_API_KEY",
    'twitter_enabled': False
}