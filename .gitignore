# API 키 및 민감한 정보
.env
*.env
upbit/env
config_with_keys.py

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 가상환경
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# 로그 파일
*.log
logs/

# 분석 결과 이미지
analysis_results/
*.png
*.jpg

# 백테스트 결과
backtest_results/
*.csv

# 임시 파일
*.tmp
*.temp

# OS
.DS_Store
Thumbs.db
