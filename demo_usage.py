"""
모듈화된 트레이딩 시스템 사용 예시
"""
import pandas as pd
import numpy as np
from trading_system import TradingSystem, create_sample_data, StrategyConfigs
import warnings
warnings.filterwarnings('ignore')


def demo_basic_usage():
    """기본 사용법 데모"""
    print("=== 기본 사용법 데모 ===")
    
    # 1. 시스템 초기화
    system = TradingSystem("default")
    print(f"시스템 초기화 완료: {system.get_system_status()['total_strategies']}개 전략 로드")
    
    # 2. 샘플 데이터 생성
    data = create_sample_data(1000)
    print(f"샘플 데이터 생성: {len(data)}개 데이터 포인트")
    
    # 3. 현재 분석
    analysis = system.analyze_symbol(data, "BTC-USD")
    print(f"\n현재 분석 결과:")
    print(f"- 복합 신호: {analysis['composite_strategy']['signal']}")
    print(f"- 신뢰도: {analysis['composite_strategy']['confidence']:.2f}")
    print(f"- 매수 신호: {analysis['summary']['buy_signals']}개")
    print(f"- 매도 신호: {analysis['summary']['sell_signals']}개")
    print(f"- 보류 신호: {analysis['summary']['hold_signals']}개")


def demo_strategy_comparison():
    """전략 비교 데모"""
    print("\n=== 전략 비교 데모 ===")
    
    # 다양한 설정으로 시스템 생성
    systems = {
        "보수적": TradingSystem("conservative"),
        "기본": TradingSystem("default"),
        "공격적": TradingSystem("aggressive")
    }
    
    # 샘플 데이터
    data = create_sample_data(2000)
    
    print("각 설정별 분석 결과:")
    for name, system in systems.items():
        analysis = system.analyze_symbol(data, "BTC-USD")
        summary = analysis['summary']
        print(f"\n{name} 설정:")
        print(f"  - 복합 신호: {analysis['composite_strategy']['signal']}")
        print(f"  - 신뢰도: {analysis['composite_strategy']['confidence']:.2f}")
        print(f"  - 매수 비율: {summary['buy_percentage']:.1f}%")
        print(f"  - 매도 비율: {summary['sell_percentage']:.1f}%")


def demo_backtesting():
    """백테스트 데모"""
    print("\n=== 백테스트 데모 ===")
    
    # 시스템 초기화
    system = TradingSystem("default")
    
    # 더 긴 기간의 데이터 생성
    data = create_sample_data(3000)
    print(f"백테스트 데이터: {len(data)}개 포인트")
    
    # 백테스트 실행
    print("백테스트 실행 중...")
    results = system.backtest_system(data, "BTC-USD")
    
    # 결과 출력
    print("\n백테스트 결과:")
    print(results['performance_report'].to_string(index=False))
    
    if results['best_strategy']:
        print(f"\n최고 성과 전략: {results['best_strategy']}")
    
    # 복합 전략 결과
    if results['composite_result']:
        composite = results['composite_result']
        print(f"\n복합 전략 성과:")
        print(f"- 총 수익률: {composite.total_return:.2%}")
        print(f"- 연환산 수익률: {composite.annual_return:.2%}")
        print(f"- 최대 낙폭: {composite.max_drawdown:.2%}")
        print(f"- 샤프 비율: {composite.sharpe_ratio:.2f}")
        print(f"- 승률: {composite.win_rate:.1%}")
        print(f"- 총 거래 수: {composite.total_trades}")


def demo_custom_strategy():
    """커스텀 전략 설정 데모"""
    print("\n=== 커스텀 전략 설정 데모 ===")
    
    # 기본 시스템 생성
    system = TradingSystem("default")
    
    # 특정 전략 비활성화
    system.disable_strategy("DoubleBottom")
    system.disable_strategy("ZScore")
    print("일부 전략 비활성화")
    
    # RSI 전략 파라미터 수정
    system.update_strategy_config("RSI", 
                                parameters={'oversold': 25, 'overbought': 75})
    print("RSI 전략 파라미터 수정")
    
    # 볼린저 밴드 가중치 증가
    rsi_strategy = system.strategy_manager.get_strategy("RSI")
    if rsi_strategy:
        rsi_strategy.weight = 1.5
        print("RSI 전략 가중치 증가")
    
    # 수정된 설정으로 분석
    data = create_sample_data(1000)
    analysis = system.analyze_symbol(data, "BTC-USD")
    
    print(f"\n커스텀 설정 분석 결과:")
    print(f"- 활성 전략 수: {analysis['summary']['total_strategies']}")
    print(f"- 복합 신호: {analysis['composite_strategy']['signal']}")
    print(f"- 신뢰도: {analysis['composite_strategy']['confidence']:.2f}")


def demo_individual_strategies():
    """개별 전략 분석 데모"""
    print("\n=== 개별 전략 분석 데모 ===")
    
    system = TradingSystem("default")
    data = create_sample_data(1000)
    
    # 개별 전략 결과 확인
    analysis = system.analyze_symbol(data, "BTC-USD")
    
    print("개별 전략 신호:")
    for name, result in analysis['individual_strategies'].items():
        print(f"- {name:15}: {result['signal']:10} (신뢰도: {result['confidence']:.2f})")
    
    print(f"\n복합 전략 최종 결정: {analysis['composite_strategy']['signal']}")


def demo_real_time_simulation():
    """실시간 시뮬레이션 데모"""
    print("\n=== 실시간 시뮬레이션 데모 ===")
    
    system = TradingSystem("default")
    
    # 긴 데이터 생성
    full_data = create_sample_data(2000)
    
    print("실시간 분석 시뮬레이션 (마지막 10개 시점):")
    
    # 마지막 10개 시점에 대해 순차적으로 분석
    for i in range(10):
        # 현재 시점까지의 데이터
        current_data = full_data.iloc[:-(10-i)]
        
        try:
            analysis = system.analyze_symbol(current_data, "BTC-USD")
            current_price = analysis['current_price']
            signal = analysis['composite_strategy']['signal']
            confidence = analysis['composite_strategy']['confidence']
            
            print(f"시점 {i+1:2d}: 가격 {current_price:7.2f} | "
                  f"신호 {signal:10} | 신뢰도 {confidence:.2f}")
        except Exception as e:
            print(f"시점 {i+1:2d}: 분석 실패 - {e}")


if __name__ == "__main__":
    print("🚀 모듈화된 트레이딩 시스템 데모")
    print("=" * 50)
    
    # 각 데모 실행
    demo_basic_usage()
    demo_strategy_comparison()
    demo_backtesting()
    demo_custom_strategy()
    demo_individual_strategies()
    demo_real_time_simulation()
    
    print("\n" + "=" * 50)
    print("✅ 모든 데모 완료!")
    print("\n사용 가능한 주요 기능:")
    print("1. 14가지 개별 전략")
    print("2. 5가지 복합 전략 투표 방식")
    print("3. 보수적/기본/공격적 설정")
    print("4. 백테스팅 및 성능 분석")
    print("5. 실시간 신호 생성")
    print("6. 커스텀 전략 설정")
    print("7. 시각화 및 리포트 생성")
