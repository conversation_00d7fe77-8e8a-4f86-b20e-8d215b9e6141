"""
모듈화된 트레이딩 시스템
"""

from .main import TradingSystem, create_sample_data
from .core.base_strategy import BaseStrategy, StrategySignal, StrategyConfig, SignalType
from .core.strategy_manager import StrategyManager, StrategyFactory
from .core.composite_strategy import CompositeStrategy, CompositeConfig, VotingMethod
from .config.strategy_configs import StrategyConfigs
from .testing.strategy_tester import StrategyTester, BacktestResult
from .utils.data_processor import DataProcessor
from .utils.indicators import TechnicalIndicators

__version__ = "1.0.0"
__author__ = "Trading System Team"

__all__ = [
    # Main system
    'TradingSystem',
    'create_sample_data',
    
    # Core components
    'BaseStrategy',
    'StrategySignal', 
    'StrategyConfig',
    'SignalType',
    'StrategyManager',
    'StrategyFactory',
    'CompositeStrategy',
    'CompositeConfig',
    'VotingMethod',
    
    # Configuration
    'StrategyConfigs',
    
    # Testing
    'StrategyTester',
    'BacktestResult',
    
    # Utilities
    'DataProcessor',
    'TechnicalIndicators'
]
