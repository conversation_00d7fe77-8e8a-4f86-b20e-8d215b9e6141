"""
전략 테스트 및 검증 시스템
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass

from ..core.base_strategy import BaseStrategy, StrategySignal, SignalType
from ..core.strategy_manager import StrategyManager
from ..core.composite_strategy import CompositeStrategy
from ..utils.data_processor import DataProcessor


@dataclass
class BacktestResult:
    """백테스트 결과 데이터 클래스"""
    strategy_name: str
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    profit_factor: float
    signals: List[StrategySignal]
    equity_curve: pd.Series
    trades: List[Dict[str, Any]]


class StrategyTester:
    """전략 테스터 클래스"""
    
    def __init__(self, initial_capital: float = 10000.0, commission: float = 0.001):
        self.initial_capital = initial_capital
        self.commission = commission
        self.logger = logging.getLogger(__name__)
    
    def backtest_single_strategy(self, strategy: BaseStrategy, df: pd.DataFrame, 
                                symbol: str = None) -> BacktestResult:
        """단일 전략 백테스트"""
        signals = []
        trades = []
        equity = [self.initial_capital]
        position = 0  # 0: 현금, 1: 롱 포지션
        entry_price = 0
        current_capital = self.initial_capital
        
        for i in range(len(df)):
            current_data = df.iloc[:i+1]
            
            if len(current_data) < strategy.get_required_periods():
                equity.append(current_capital)
                continue
            
            # 전략 신호 계산
            signal = strategy.execute(current_data, symbol)
            signals.append(signal)
            
            current_price = df['close'].iloc[i]
            
            # 매매 실행
            if signal.signal in [SignalType.BUY, SignalType.STRONG_BUY] and position == 0:
                # 매수
                position = 1
                entry_price = current_price * (1 + self.commission)
                trades.append({
                    'type': 'buy',
                    'price': entry_price,
                    'timestamp': df.index[i] if hasattr(df.index, 'to_pydatetime') else i,
                    'signal_confidence': signal.confidence
                })
            
            elif signal.signal in [SignalType.SELL, SignalType.STRONG_SELL] and position == 1:
                # 매도
                exit_price = current_price * (1 - self.commission)
                trade_return = (exit_price - entry_price) / entry_price
                current_capital *= (1 + trade_return)
                
                trades.append({
                    'type': 'sell',
                    'price': exit_price,
                    'timestamp': df.index[i] if hasattr(df.index, 'to_pydatetime') else i,
                    'return': trade_return,
                    'signal_confidence': signal.confidence
                })
                
                position = 0
                entry_price = 0
            
            # 현재 자본 계산 (포지션 보유 중인 경우)
            if position == 1:
                unrealized_return = (current_price - entry_price) / entry_price
                current_equity = self.initial_capital * (1 + unrealized_return)
            else:
                current_equity = current_capital
            
            equity.append(current_equity)
        
        # 결과 계산
        equity_series = pd.Series(equity, index=df.index if len(equity) == len(df) else range(len(equity)))
        return self._calculate_backtest_metrics(strategy.name, equity_series, trades, signals)
    
    def backtest_composite_strategy(self, composite_strategy: CompositeStrategy, 
                                  df: pd.DataFrame, symbol: str = None) -> BacktestResult:
        """복합 전략 백테스트"""
        signals = []
        trades = []
        equity = [self.initial_capital]
        position = 0
        entry_price = 0
        current_capital = self.initial_capital
        
        required_periods = composite_strategy.strategy_manager.get_required_periods()
        
        for i in range(len(df)):
            current_data = df.iloc[:i+1]
            
            if len(current_data) < required_periods:
                equity.append(current_capital)
                continue
            
            # 복합 전략 신호 계산
            signal = composite_strategy.calculate_composite_signal(current_data, symbol)
            signals.append(signal)
            
            current_price = df['close'].iloc[i]
            
            # 매매 실행 (단일 전략과 동일한 로직)
            if signal.signal in [SignalType.BUY, SignalType.STRONG_BUY] and position == 0:
                position = 1
                entry_price = current_price * (1 + self.commission)
                trades.append({
                    'type': 'buy',
                    'price': entry_price,
                    'timestamp': df.index[i] if hasattr(df.index, 'to_pydatetime') else i,
                    'signal_confidence': signal.confidence
                })
            
            elif signal.signal in [SignalType.SELL, SignalType.STRONG_SELL] and position == 1:
                exit_price = current_price * (1 - self.commission)
                trade_return = (exit_price - entry_price) / entry_price
                current_capital *= (1 + trade_return)
                
                trades.append({
                    'type': 'sell',
                    'price': exit_price,
                    'timestamp': df.index[i] if hasattr(df.index, 'to_pydatetime') else i,
                    'return': trade_return,
                    'signal_confidence': signal.confidence
                })
                
                position = 0
                entry_price = 0
            
            if position == 1:
                unrealized_return = (current_price - entry_price) / entry_price
                current_equity = self.initial_capital * (1 + unrealized_return)
            else:
                current_equity = current_capital
            
            equity.append(current_equity)
        
        equity_series = pd.Series(equity, index=df.index if len(equity) == len(df) else range(len(equity)))
        return self._calculate_backtest_metrics("Composite_Strategy", equity_series, trades, signals)
    
    def _calculate_backtest_metrics(self, strategy_name: str, equity_curve: pd.Series, 
                                  trades: List[Dict], signals: List[StrategySignal]) -> BacktestResult:
        """백테스트 메트릭 계산"""
        # 기본 수익률 계산
        total_return = (equity_curve.iloc[-1] - equity_curve.iloc[0]) / equity_curve.iloc[0]
        
        # 연환산 수익률 (일 단위 가정)
        days = len(equity_curve)
        annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        
        # 최대 낙폭 계산
        peak = equity_curve.expanding().max()
        drawdown = (equity_curve - peak) / peak
        max_drawdown = drawdown.min()
        
        # 샤프 비율 계산
        returns = equity_curve.pct_change().dropna()
        if len(returns) > 1 and returns.std() > 0:
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252)  # 연환산
        else:
            sharpe_ratio = 0
        
        # 거래 분석
        completed_trades = [t for t in trades if 'return' in t]
        total_trades = len(completed_trades)
        
        if total_trades > 0:
            winning_trades = len([t for t in completed_trades if t['return'] > 0])
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades
            
            wins = [t['return'] for t in completed_trades if t['return'] > 0]
            losses = [t['return'] for t in completed_trades if t['return'] <= 0]
            
            avg_win = np.mean(wins) if wins else 0
            avg_loss = np.mean(losses) if losses else 0
            
            gross_profit = sum(wins) if wins else 0
            gross_loss = abs(sum(losses)) if losses else 0
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        else:
            winning_trades = losing_trades = 0
            win_rate = avg_win = avg_loss = profit_factor = 0
        
        return BacktestResult(
            strategy_name=strategy_name,
            total_return=total_return,
            annual_return=annual_return,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            signals=signals,
            equity_curve=equity_curve,
            trades=completed_trades
        )
    
    def compare_strategies(self, strategies: List[BaseStrategy], df: pd.DataFrame, 
                          symbol: str = None) -> Dict[str, BacktestResult]:
        """여러 전략 비교"""
        results = {}
        
        for strategy in strategies:
            try:
                result = self.backtest_single_strategy(strategy, df, symbol)
                results[strategy.name] = result
                self.logger.info(f"Backtested strategy: {strategy.name}")
            except Exception as e:
                self.logger.error(f"Error backtesting {strategy.name}: {e}")
        
        return results
    
    def generate_performance_report(self, results: Dict[str, BacktestResult]) -> pd.DataFrame:
        """성능 리포트 생성"""
        report_data = []
        
        for name, result in results.items():
            report_data.append({
                'Strategy': name,
                'Total Return (%)': result.total_return * 100,
                'Annual Return (%)': result.annual_return * 100,
                'Max Drawdown (%)': result.max_drawdown * 100,
                'Sharpe Ratio': result.sharpe_ratio,
                'Win Rate (%)': result.win_rate * 100,
                'Total Trades': result.total_trades,
                'Profit Factor': result.profit_factor,
                'Avg Win (%)': result.avg_win * 100,
                'Avg Loss (%)': result.avg_loss * 100
            })
        
        return pd.DataFrame(report_data).round(2)
    
    def plot_equity_curves(self, results: Dict[str, BacktestResult], save_path: str = None):
        """자본 곡선 시각화"""
        plt.figure(figsize=(12, 8))
        
        for name, result in results.items():
            plt.plot(result.equity_curve.index, result.equity_curve.values, 
                    label=f"{name} ({result.total_return:.1%})", linewidth=2)
        
        plt.title('Strategy Performance Comparison - Equity Curves')
        plt.xlabel('Time')
        plt.ylabel('Portfolio Value')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_performance_metrics(self, results: Dict[str, BacktestResult], save_path: str = None):
        """성능 메트릭 시각화"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        strategies = list(results.keys())
        returns = [results[s].total_return * 100 for s in strategies]
        sharpe_ratios = [results[s].sharpe_ratio for s in strategies]
        max_drawdowns = [abs(results[s].max_drawdown) * 100 for s in strategies]
        win_rates = [results[s].win_rate * 100 for s in strategies]
        
        # 총 수익률
        axes[0, 0].bar(strategies, returns)
        axes[0, 0].set_title('Total Return (%)')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 샤프 비율
        axes[0, 1].bar(strategies, sharpe_ratios)
        axes[0, 1].set_title('Sharpe Ratio')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 최대 낙폭
        axes[1, 0].bar(strategies, max_drawdowns, color='red', alpha=0.7)
        axes[1, 0].set_title('Max Drawdown (%)')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 승률
        axes[1, 1].bar(strategies, win_rates, color='green', alpha=0.7)
        axes[1, 1].set_title('Win Rate (%)')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
