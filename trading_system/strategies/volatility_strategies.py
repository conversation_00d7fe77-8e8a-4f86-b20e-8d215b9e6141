"""
변동성 기반 전략들
"""
import pandas as pd
import numpy as np
from typing import Optional

from ..core.base_strategy import BaseStrategy, StrategySignal, StrategyConfig, SignalType
from ..core.strategy_manager import strategy_decorator
from ..utils.indicators import TechnicalIndicators


@strategy_decorator("Bollinger")
class BollingerBandsStrategy(BaseStrategy):
    """볼린저 밴드 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.window = self.parameters.get('window', 20)
        self.std_dev = self.parameters.get('std_dev', 2.0)
    
    def get_required_periods(self) -> int:
        return self.window + 10
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """볼린저 밴드 신호 계산"""
        upper_band, middle_band, lower_band = TechnicalIndicators.bollinger_bands(
            df['close'], self.window, self.std_dev
        )
        
        current_price = df['close'].iloc[-1]
        current_upper = upper_band.iloc[-1]
        current_lower = lower_band.iloc[-1]
        current_middle = middle_band.iloc[-1]
        
        # 신호 결정
        if pd.isna(current_upper) or pd.isna(current_lower):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif current_price > current_upper:
            signal_type = SignalType.SELL
            # 상단 밴드를 넘어선 정도에 따른 신뢰도
            confidence = min(1.0, (current_price - current_upper) / (current_upper - current_middle))
        elif current_price < current_lower:
            signal_type = SignalType.BUY
            # 하단 밴드를 넘어선 정도에 따른 신뢰도
            confidence = min(1.0, (current_lower - current_price) / (current_middle - current_lower))
        else:
            signal_type = SignalType.HOLD
            confidence = 0.3
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'upper_band': current_upper,
                'middle_band': current_middle,
                'lower_band': current_lower,
                'band_width': (current_upper - current_lower) / current_middle,
                'position': 'above_upper' if current_price > current_upper else 
                           'below_lower' if current_price < current_lower else 'inside'
            }
        )


@strategy_decorator("ZScore")
class ZScoreStrategy(BaseStrategy):
    """Z-Score 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.window = self.parameters.get('window', 20)
        self.buy_threshold = self.parameters.get('buy_threshold', -2.0)
        self.sell_threshold = self.parameters.get('sell_threshold', 2.0)
    
    def get_required_periods(self) -> int:
        return self.window + 10
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """Z-Score 신호 계산"""
        zscore = TechnicalIndicators.zscore(df['close'], self.window)
        current_zscore = zscore.iloc[-1]
        current_price = df['close'].iloc[-1]
        
        # 신호 결정
        if pd.isna(current_zscore):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif current_zscore < self.buy_threshold:
            signal_type = SignalType.BUY
            confidence = min(1.0, abs(current_zscore - self.buy_threshold) / abs(self.buy_threshold))
        elif current_zscore > self.sell_threshold:
            signal_type = SignalType.SELL
            confidence = min(1.0, abs(current_zscore - self.sell_threshold) / self.sell_threshold)
        else:
            signal_type = SignalType.HOLD
            confidence = 0.4
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'zscore': current_zscore,
                'buy_threshold': self.buy_threshold,
                'sell_threshold': self.sell_threshold
            }
        )


@strategy_decorator("DoubleBottom")
class DoubleBottomStrategy(BaseStrategy):
    """더블 바텀 패턴 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.window = self.parameters.get('window', 10)
        self.tolerance = self.parameters.get('tolerance', 0.02)
    
    def get_required_periods(self) -> int:
        return self.window * 3
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """더블 바텀 패턴 신호 계산"""
        double_bottom_signals = TechnicalIndicators.detect_double_bottom(
            df['low'], self.window, self.tolerance
        )
        
        current_price = df['close'].iloc[-1]
        recent_signals = double_bottom_signals.tail(5)  # 최근 5개 캔들 확인
        
        # 최근에 더블 바텀 패턴이 감지되었는지 확인
        if recent_signals.any():
            signal_type = SignalType.BUY
            confidence = 0.7  # 패턴 기반 전략은 중간 신뢰도
        else:
            signal_type = SignalType.HOLD
            confidence = 0.2
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'pattern_detected': recent_signals.any(),
                'recent_low': df['low'].tail(self.window).min(),
                'tolerance': self.tolerance
            }
        )
