"""
RSI 기반 전략들
"""
import pandas as pd
import numpy as np
from typing import Optional

from ..core.base_strategy import BaseStrategy, StrategySignal, StrategyConfig, SignalType
from ..core.strategy_manager import strategy_decorator
from ..utils.indicators import TechnicalIndicators


@strategy_decorator("RSI")
class RSIStrategy(BaseStrategy):
    """기본 RSI 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.window = self.parameters.get('window', 14)
        self.oversold = self.parameters.get('oversold', 30)
        self.overbought = self.parameters.get('overbought', 70)
    
    def get_required_periods(self) -> int:
        return self.window + 10
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """RSI 기반 매매 신호 계산"""
        rsi = TechnicalIndicators.rsi(df['close'], self.window)
        current_rsi = rsi.iloc[-1]
        current_price = df['close'].iloc[-1]
        
        # 신호 결정
        if pd.isna(current_rsi):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif current_rsi < self.oversold:
            signal_type = SignalType.BUY
            confidence = min(1.0, (self.oversold - current_rsi) / self.oversold)
        elif current_rsi > self.overbought:
            signal_type = SignalType.SELL
            confidence = min(1.0, (current_rsi - self.overbought) / (100 - self.overbought))
        else:
            signal_type = SignalType.HOLD
            confidence = 0.5
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'rsi': current_rsi,
                'oversold_level': self.oversold,
                'overbought_level': self.overbought
            }
        )


@strategy_decorator("RSI_Histogram")
class RSIHistogramStrategy(BaseStrategy):
    """RSI 히스토그램 크로스오버 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.rsi_window = self.parameters.get('rsi_window', 14)
        self.signal_window = self.parameters.get('signal_window', 9)
    
    def get_required_periods(self) -> int:
        return max(self.rsi_window, self.signal_window) + 10
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """RSI 히스토그램 크로스오버 신호 계산"""
        rsi = TechnicalIndicators.rsi(df['close'], self.rsi_window)
        rsi_signal = rsi.rolling(self.signal_window).mean()
        rsi_histogram = rsi - rsi_signal
        
        current_hist = rsi_histogram.iloc[-1]
        prev_hist = rsi_histogram.iloc[-2]
        current_price = df['close'].iloc[-1]
        
        # 크로스오버 감지
        if pd.isna(current_hist) or pd.isna(prev_hist):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif current_hist > 0 and prev_hist <= 0:
            signal_type = SignalType.BUY
            confidence = min(1.0, abs(current_hist) / 10)  # 히스토그램 값에 따른 신뢰도
        elif current_hist < 0 and prev_hist >= 0:
            signal_type = SignalType.SELL
            confidence = min(1.0, abs(current_hist) / 10)
        else:
            signal_type = SignalType.HOLD
            confidence = 0.3
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'rsi': rsi.iloc[-1],
                'rsi_signal': rsi_signal.iloc[-1],
                'histogram': current_hist,
                'prev_histogram': prev_hist
            }
        )


@strategy_decorator("RSI_Combo_Strong")
class RSIComboStrongStrategy(BaseStrategy):
    """RSI 복합 강화 전략 (1분봉 + 30분봉)"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.rsi_window = self.parameters.get('rsi_window', 55)
        self.signal_window = self.parameters.get('signal_window', 62)
    
    def get_required_periods(self) -> int:
        return max(self.rsi_window, self.signal_window) + 10
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """RSI 복합 강화 신호 계산"""
        rsi = TechnicalIndicators.rsi(df['close'], self.rsi_window)
        rsi_signal = rsi.rolling(self.signal_window).mean()
        
        current_rsi = rsi.iloc[-1]
        current_signal = rsi_signal.iloc[-1]
        current_price = df['close'].iloc[-1]
        
        # 신호 결정
        if pd.isna(current_rsi) or pd.isna(current_signal):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif current_rsi > current_signal and current_rsi >= 50:
            # 추가적으로 30분봉 데이터가 있다면 더 강한 신호
            if current_rsi >= 55:
                signal_type = SignalType.STRONG_BUY
                confidence = min(1.0, (current_rsi - 50) / 50)
            else:
                signal_type = SignalType.BUY
                confidence = min(1.0, (current_rsi - 50) / 50)
        elif current_rsi < current_signal and current_rsi <= 49:
            signal_type = SignalType.SELL
            confidence = min(1.0, (50 - current_rsi) / 50)
        else:
            signal_type = SignalType.HOLD
            confidence = 0.4
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'rsi': current_rsi,
                'rsi_signal': current_signal,
                'rsi_diff': current_rsi - current_signal
            }
        )


@strategy_decorator("DFT_RSI")
class DFTRSIStrategy(BaseStrategy):
    """Ehlers DFT 적응형 RSI 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.window = self.parameters.get('window', 50)
        self.frac = self.parameters.get('frac', 0.5)
        self.fast_span = self.parameters.get('fast_span', 13)
        self.slow_span = self.parameters.get('slow_span', 89)
    
    def get_required_periods(self) -> int:
        return max(self.window, self.slow_span) + 20
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """DFT 적응형 RSI 신호 계산"""
        dft_rsi = TechnicalIndicators.ehlers_dft_adapted_rsi(df, self.window, self.frac)
        fast_ema = dft_rsi.ewm(span=self.fast_span).mean()
        slow_ema = dft_rsi.ewm(span=self.slow_span).mean()
        
        current_fast = fast_ema.iloc[-1]
        current_slow = slow_ema.iloc[-1]
        prev_fast = fast_ema.iloc[-2]
        prev_slow = slow_ema.iloc[-2]
        current_price = df['close'].iloc[-1]
        
        # 크로스오버 감지
        if (pd.isna(current_fast) or pd.isna(current_slow) or 
            pd.isna(prev_fast) or pd.isna(prev_slow)):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif current_fast > current_slow and prev_fast <= prev_slow:
            signal_type = SignalType.BUY
            confidence = min(1.0, abs(current_fast - current_slow) / 20)
        elif current_fast < current_slow and prev_fast >= prev_slow:
            signal_type = SignalType.SELL
            confidence = min(1.0, abs(current_fast - current_slow) / 20)
        else:
            signal_type = SignalType.HOLD
            confidence = 0.3
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'dft_rsi': dft_rsi.iloc[-1],
                'fast_ema': current_fast,
                'slow_ema': current_slow,
                'crossover': current_fast > current_slow
            }
        )
