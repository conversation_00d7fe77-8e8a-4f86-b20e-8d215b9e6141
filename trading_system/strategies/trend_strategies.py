"""
트렌드 기반 전략들
"""
import pandas as pd
import numpy as np
from typing import Optional

from ..core.base_strategy import BaseStrategy, StrategySignal, StrategyConfig, SignalType
from ..core.strategy_manager import strategy_decorator
from ..utils.indicators import TechnicalIndicators


@strategy_decorator("MACD")
class MACDStrategy(BaseStrategy):
    """MACD 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.fast = self.parameters.get('fast', 12)
        self.slow = self.parameters.get('slow', 26)
        self.signal = self.parameters.get('signal', 9)
    
    def get_required_periods(self) -> int:
        return self.slow + self.signal + 10
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """MACD 신호 계산"""
        macd_line, signal_line, histogram = TechnicalIndicators.macd(
            df['close'], self.fast, self.slow, self.signal
        )
        
        current_macd = macd_line.iloc[-1]
        current_signal = signal_line.iloc[-1]
        current_price = df['close'].iloc[-1]
        
        # 신호 결정
        if pd.isna(current_macd) or pd.isna(current_signal):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif current_macd > current_signal:
            signal_type = SignalType.BUY
            confidence = min(1.0, abs(current_macd - current_signal) / abs(current_macd) if current_macd != 0 else 0.5)
        elif current_macd < current_signal:
            signal_type = SignalType.SELL
            confidence = min(1.0, abs(current_macd - current_signal) / abs(current_macd) if current_macd != 0 else 0.5)
        else:
            signal_type = SignalType.HOLD
            confidence = 0.3
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'macd': current_macd,
                'signal': current_signal,
                'histogram': histogram.iloc[-1] if not histogram.empty else 0
            }
        )


@strategy_decorator("Ichimoku")
class IchimokuStrategy(BaseStrategy):
    """이치모쿠 클라우드 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.window1 = self.parameters.get('window1', 9)
        self.window2 = self.parameters.get('window2', 26)
        self.window3 = self.parameters.get('window3', 52)
    
    def get_required_periods(self) -> int:
        return self.window3 + 26  # 선행스팬 때문에 추가 기간 필요
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """이치모쿠 신호 계산"""
        ichimoku = TechnicalIndicators.ichimoku(
            df['high'], df['low'], self.window1, self.window2, self.window3
        )
        
        current_price = df['close'].iloc[-1]
        senkou_span_a = ichimoku['senkou_span_a'].iloc[-1]
        senkou_span_b = ichimoku['senkou_span_b'].iloc[-1]
        
        # 클라우드 위/아래 판단
        if pd.isna(senkou_span_a) or pd.isna(senkou_span_b):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif current_price > max(senkou_span_a, senkou_span_b):
            signal_type = SignalType.BUY
            cloud_distance = current_price - max(senkou_span_a, senkou_span_b)
            confidence = min(1.0, cloud_distance / current_price * 100)
        elif current_price < min(senkou_span_a, senkou_span_b):
            signal_type = SignalType.SELL
            cloud_distance = min(senkou_span_a, senkou_span_b) - current_price
            confidence = min(1.0, cloud_distance / current_price * 100)
        else:
            signal_type = SignalType.HOLD
            confidence = 0.2  # 클라우드 내부는 중립
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'senkou_span_a': senkou_span_a,
                'senkou_span_b': senkou_span_b,
                'tenkan_sen': ichimoku['tenkan_sen'].iloc[-1],
                'kijun_sen': ichimoku['kijun_sen'].iloc[-1],
                'cloud_position': 'above' if current_price > max(senkou_span_a, senkou_span_b) else 
                                'below' if current_price < min(senkou_span_a, senkou_span_b) else 'inside'
            }
        )


@strategy_decorator("Multi_Ichimoku")
class MultiIchimokuStrategy(BaseStrategy):
    """멀티 이치모쿠 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        # 첫 번째 이치모쿠 설정
        self.window1_1 = self.parameters.get('window1_1', 9)
        self.window2_1 = self.parameters.get('window2_1', 26)
        self.window3_1 = self.parameters.get('window3_1', 52)
        # 두 번째 이치모쿠 설정
        self.window1_2 = self.parameters.get('window1_2', 27)
        self.window2_2 = self.parameters.get('window2_2', 78)
        self.window3_2 = self.parameters.get('window3_2', 156)
    
    def get_required_periods(self) -> int:
        return max(self.window3_1, self.window3_2) + 50
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """멀티 이치모쿠 신호 계산"""
        # 첫 번째 이치모쿠
        ichimoku1 = TechnicalIndicators.ichimoku(
            df['high'], df['low'], self.window1_1, self.window2_1, self.window3_1
        )
        
        # 두 번째 이치모쿠
        ichimoku2 = TechnicalIndicators.ichimoku(
            df['high'], df['low'], self.window1_2, self.window2_2, self.window3_2
        )
        
        current_price = df['close'].iloc[-1]
        
        # 첫 번째 이치모쿠 신호
        span_a1 = ichimoku1['senkou_span_a'].iloc[-1]
        span_b1 = ichimoku1['senkou_span_b'].iloc[-1]
        
        # 두 번째 이치모쿠 신호
        span_a2 = ichimoku2['senkou_span_a'].iloc[-1]
        span_b2 = ichimoku2['senkou_span_b'].iloc[-1]
        
        # 신호 결정
        if any(pd.isna(x) for x in [span_a1, span_b1, span_a2, span_b2]):
            signal_type = SignalType.HOLD
            confidence = 0.0
        else:
            buy1 = span_a1 > span_b1
            buy2 = span_a2 > span_b2
            sell1 = span_a1 < span_b1
            sell2 = span_a2 < span_b2
            
            if (buy1 or buy2) and not (sell1 and sell2):
                signal_type = SignalType.BUY
                confidence = 0.8 if (buy1 and buy2) else 0.6
            elif (sell1 or sell2) and not (buy1 and buy2):
                signal_type = SignalType.SELL
                confidence = 0.8 if (sell1 and sell2) else 0.6
            else:
                signal_type = SignalType.HOLD
                confidence = 0.3
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'ichimoku1_span_a': span_a1,
                'ichimoku1_span_b': span_b1,
                'ichimoku2_span_a': span_a2,
                'ichimoku2_span_b': span_b2,
                'buy_signals': sum([span_a1 > span_b1, span_a2 > span_b2]),
                'sell_signals': sum([span_a1 < span_b1, span_a2 < span_b2])
            }
        )


@strategy_decorator("WaveTrend")
class WaveTrendStrategy(BaseStrategy):
    """WaveTrend 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.channel_length = self.parameters.get('channel_length', 10)
        self.average_length = self.parameters.get('average_length', 21)
        self.resample_period = self.parameters.get('resample_period', '10min')
    
    def get_required_periods(self) -> int:
        return self.average_length + 20
    
    def _resample_to_period(self, df: pd.DataFrame) -> pd.DataFrame:
        """데이터를 지정된 기간으로 리샘플링"""
        try:
            df_copy = df.copy()
            df_copy.index = pd.to_datetime(df_copy.index)
            df_resampled = df_copy.resample(self.resample_period).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            return df_resampled
        except Exception:
            return df.copy()
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """WaveTrend 신호 계산"""
        # 10분봉으로 리샘플링
        df_resampled = self._resample_to_period(df)
        
        if len(df_resampled) < self.average_length:
            return StrategySignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                timestamp=pd.Timestamp.now(),
                price=df['close'].iloc[-1],
                metadata={'error': 'insufficient_resampled_data'}
            )
        
        wt1, wt2 = TechnicalIndicators.wavetrend(
            df_resampled['high'], df_resampled['low'], df_resampled['close'],
            self.channel_length, self.average_length
        )
        
        current_wt1 = wt1.iloc[-1]
        current_wt2 = wt2.iloc[-1]
        current_price = df['close'].iloc[-1]
        
        # 신호 결정
        if pd.isna(current_wt1) or pd.isna(current_wt2):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif current_wt1 > current_wt2:
            signal_type = SignalType.BUY
            confidence = min(1.0, abs(current_wt1 - current_wt2) / 50)
        elif current_wt1 < current_wt2:
            signal_type = SignalType.SELL
            confidence = min(1.0, abs(current_wt1 - current_wt2) / 50)
        else:
            signal_type = SignalType.HOLD
            confidence = 0.3
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'wt1': current_wt1,
                'wt2': current_wt2,
                'wt_diff': current_wt1 - current_wt2
            }
        )
