"""
볼륨 기반 전략들
"""
import pandas as pd
import numpy as np
from typing import Optional

from ..core.base_strategy import BaseStrategy, StrategySignal, StrategyConfig, SignalType
from ..core.strategy_manager import strategy_decorator
from ..utils.indicators import TechnicalIndicators


@strategy_decorator("PVT_Cross")
class PVTCrossStrategy(BaseStrategy):
    """PVT 크로스 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.ma_window = self.parameters.get('ma_window', 20)
    
    def get_required_periods(self) -> int:
        return self.ma_window + 10
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """PVT 크로스 신호 계산"""
        pvt = TechnicalIndicators.pvt(df['close'], df['volume'])
        pvt_ma = pvt.rolling(self.ma_window).mean()
        
        current_pvt = pvt.iloc[-1]
        current_ma = pvt_ma.iloc[-1]
        prev_pvt = pvt.iloc[-2]
        prev_ma = pvt_ma.iloc[-2]
        current_price = df['close'].iloc[-1]
        
        # 크로스오버 감지
        if any(pd.isna(x) for x in [current_pvt, current_ma, prev_pvt, prev_ma]):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif current_pvt > current_ma and prev_pvt <= prev_ma:
            signal_type = SignalType.BUY
            confidence = min(1.0, abs(current_pvt - current_ma) / abs(current_ma) if current_ma != 0 else 0.5)
        elif current_pvt < current_ma and prev_pvt >= prev_ma:
            signal_type = SignalType.SELL
            confidence = min(1.0, abs(current_pvt - current_ma) / abs(current_ma) if current_ma != 0 else 0.5)
        else:
            signal_type = SignalType.HOLD
            confidence = 0.3
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'pvt': current_pvt,
                'pvt_ma': current_ma,
                'pvt_diff': current_pvt - current_ma,
                'crossover_type': 'bullish' if current_pvt > current_ma else 'bearish'
            }
        )


@strategy_decorator("OBV_Trend")
class OBVTrendStrategy(BaseStrategy):
    """OBV 트렌드 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.short_ma = self.parameters.get('short_ma', 10)
        self.long_ma = self.parameters.get('long_ma', 30)
    
    def get_required_periods(self) -> int:
        return self.long_ma + 10
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """OBV 트렌드 신호 계산"""
        obv = TechnicalIndicators.obv(df['close'], df['volume'])
        obv_short_ma = obv.rolling(self.short_ma).mean()
        obv_long_ma = obv.rolling(self.long_ma).mean()
        
        current_short = obv_short_ma.iloc[-1]
        current_long = obv_long_ma.iloc[-1]
        prev_short = obv_short_ma.iloc[-2]
        prev_long = obv_long_ma.iloc[-2]
        current_price = df['close'].iloc[-1]
        
        # 골든/데드 크로스 감지
        if any(pd.isna(x) for x in [current_short, current_long, prev_short, prev_long]):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif current_short > current_long and prev_short <= prev_long:
            signal_type = SignalType.BUY
            confidence = min(1.0, abs(current_short - current_long) / abs(current_long) if current_long != 0 else 0.5)
        elif current_short < current_long and prev_short >= prev_long:
            signal_type = SignalType.SELL
            confidence = min(1.0, abs(current_short - current_long) / abs(current_long) if current_long != 0 else 0.5)
        else:
            # 트렌드 방향 확인
            if current_short > current_long:
                signal_type = SignalType.HOLD  # 상승 트렌드 유지
                confidence = 0.4
            else:
                signal_type = SignalType.HOLD  # 하락 트렌드 유지
                confidence = 0.4
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'obv': obv.iloc[-1],
                'obv_short_ma': current_short,
                'obv_long_ma': current_long,
                'trend': 'bullish' if current_short > current_long else 'bearish'
            }
        )


@strategy_decorator("Volume_Breakout")
class VolumeBreakoutStrategy(BaseStrategy):
    """볼륨 브레이크아웃 전략"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.volume_ma_window = self.parameters.get('volume_ma_window', 20)
        self.volume_multiplier = self.parameters.get('volume_multiplier', 2.0)
        self.price_change_threshold = self.parameters.get('price_change_threshold', 0.02)
    
    def get_required_periods(self) -> int:
        return self.volume_ma_window + 5
    
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """볼륨 브레이크아웃 신호 계산"""
        volume_ma = df['volume'].rolling(self.volume_ma_window).mean()
        current_volume = df['volume'].iloc[-1]
        current_price = df['close'].iloc[-1]
        prev_price = df['close'].iloc[-2]
        
        # 가격 변화율 계산
        price_change = (current_price - prev_price) / prev_price
        
        # 볼륨 스파이크 감지
        volume_spike = current_volume > (volume_ma.iloc[-1] * self.volume_multiplier)
        significant_price_move = abs(price_change) > self.price_change_threshold
        
        if pd.isna(volume_ma.iloc[-1]):
            signal_type = SignalType.HOLD
            confidence = 0.0
        elif volume_spike and significant_price_move:
            if price_change > 0:
                signal_type = SignalType.BUY
            else:
                signal_type = SignalType.SELL
            confidence = min(1.0, abs(price_change) * 10)  # 가격 변화에 비례한 신뢰도
        else:
            signal_type = SignalType.HOLD
            confidence = 0.2
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'current_volume': current_volume,
                'volume_ma': volume_ma.iloc[-1],
                'volume_ratio': current_volume / volume_ma.iloc[-1] if volume_ma.iloc[-1] > 0 else 0,
                'price_change': price_change,
                'volume_spike': volume_spike,
                'significant_move': significant_price_move
            }
        )
