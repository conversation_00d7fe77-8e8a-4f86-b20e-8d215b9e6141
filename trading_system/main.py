"""
통합 트레이딩 시스템 메인 실행 파일
"""
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any
import warnings
warnings.filterwarnings('ignore')

# 전략 모듈 임포트
from .strategies.rsi_strategy import RSIStrategy, RSIHistogramStrategy, RSIComboStrongStrategy, DFTRSIStrategy
from .strategies.trend_strategies import MACDStrategy, IchimokuStrategy, MultiIchimokuStrategy, WaveTrendStrategy
from .strategies.volatility_strategies import BollingerBandsStrategy, ZScoreStrategy, DoubleBottomStrategy
from .strategies.volume_strategies import PVTCrossStrategy, OBVTrendStrategy, VolumeBreakoutStrategy

# 코어 모듈 임포트
from .core.strategy_manager import StrategyManager, StrategyFactory
from .core.composite_strategy import CompositeStrategy, CompositeConfig, VotingMethod
from .config.strategy_configs import StrategyConfigs
from .testing.strategy_tester import StrategyTester
from .utils.data_processor import DataProcessor

# 로깅 설정
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


class TradingSystem:
    """통합 트레이딩 시스템"""
    
    def __init__(self, config_type: str = "default"):
        """
        Args:
            config_type: 설정 타입 ("default", "conservative", "aggressive")
        """
        self.logger = logging.getLogger(__name__)
        self.config_type = config_type
        
        # 전략 매니저 초기화
        self.strategy_manager = StrategyManager()
        
        # 전략 설정 로드
        self._load_strategies()
        
        # 복합전략 설정
        composite_configs = StrategyConfigs.get_composite_configs()
        self.composite_strategy = CompositeStrategy(
            self.strategy_manager, 
            composite_configs.get(config_type, composite_configs["default"])
        )
        
        # 테스터 초기화
        self.tester = StrategyTester()
        
        self.logger.info(f"Trading System initialized with {config_type} configuration")
    
    def _load_strategies(self):
        """전략들 로드"""
        # 설정 타입에 따른 전략 설정 로드
        if self.config_type == "conservative":
            configs = StrategyConfigs.get_conservative_configs()
        elif self.config_type == "aggressive":
            configs = StrategyConfigs.get_aggressive_configs()
        else:
            configs = StrategyConfigs.get_default_configs()
        
        # 각 전략 생성 및 추가
        strategy_classes = {
            "RSI": RSIStrategy,
            "RSI_Histogram": RSIHistogramStrategy,
            "RSI_Combo_Strong": RSIComboStrongStrategy,
            "DFT_RSI": DFTRSIStrategy,
            "MACD": MACDStrategy,
            "Ichimoku": IchimokuStrategy,
            "Multi_Ichimoku": MultiIchimokuStrategy,
            "WaveTrend": WaveTrendStrategy,
            "Bollinger": BollingerBandsStrategy,
            "ZScore": ZScoreStrategy,
            "DoubleBottom": DoubleBottomStrategy,
            "PVT_Cross": PVTCrossStrategy,
            "OBV_Trend": OBVTrendStrategy,
            "Volume_Breakout": VolumeBreakoutStrategy
        }
        
        for name, config in configs.items():
            if name in strategy_classes:
                try:
                    strategy = strategy_classes[name](config)
                    self.strategy_manager.add_strategy(strategy)
                except Exception as e:
                    self.logger.error(f"Failed to load strategy {name}: {e}")
    
    def analyze_symbol(self, df: pd.DataFrame, symbol: str = None) -> Dict[str, Any]:
        """심볼 분석"""
        # 데이터 유효성 검증
        if not DataProcessor.validate_ohlcv_data(df):
            raise ValueError("Invalid OHLCV data")
        
        # 데이터 정리
        df_clean = DataProcessor.clean_ohlcv_data(df)
        
        # 개별 전략 실행
        strategy_results = self.strategy_manager.execute_all_strategies(df_clean, symbol)
        
        # 복합 전략 실행
        composite_signal = self.composite_strategy.calculate_composite_signal(df_clean, symbol)
        
        # 결과 정리
        analysis_result = {
            'symbol': symbol,
            'timestamp': pd.Timestamp.now(),
            'current_price': df_clean['close'].iloc[-1],
            'data_points': len(df_clean),
            'individual_strategies': {
                name: {
                    'signal': signal.signal.value,
                    'confidence': signal.confidence,
                    'metadata': signal.metadata
                }
                for name, signal in strategy_results.items()
            },
            'composite_strategy': {
                'signal': composite_signal.signal.value,
                'confidence': composite_signal.confidence,
                'metadata': composite_signal.metadata
            },
            'summary': self.composite_strategy.get_signal_summary()
        }
        
        return analysis_result
    
    def backtest_system(self, df: pd.DataFrame, symbol: str = None) -> Dict[str, Any]:
        """시스템 백테스트"""
        self.logger.info(f"Starting backtest for {symbol}")
        
        # 데이터 검증 및 정리
        if not DataProcessor.validate_ohlcv_data(df):
            raise ValueError("Invalid OHLCV data for backtesting")
        
        df_clean = DataProcessor.clean_ohlcv_data(df)
        
        # 개별 전략 백테스트
        individual_results = {}
        enabled_strategies = self.strategy_manager.get_enabled_strategies()
        
        for name, strategy in enabled_strategies.items():
            try:
                result = self.tester.backtest_single_strategy(strategy, df_clean, symbol)
                individual_results[name] = result
                self.logger.info(f"Backtested {name}: {result.total_return:.2%} return")
            except Exception as e:
                self.logger.error(f"Backtest failed for {name}: {e}")
        
        # 복합 전략 백테스트
        try:
            composite_result = self.tester.backtest_composite_strategy(
                self.composite_strategy, df_clean, symbol
            )
            self.logger.info(f"Composite strategy: {composite_result.total_return:.2%} return")
        except Exception as e:
            self.logger.error(f"Composite backtest failed: {e}")
            composite_result = None
        
        # 성능 리포트 생성
        all_results = individual_results.copy()
        if composite_result:
            all_results['Composite'] = composite_result
        
        performance_report = self.tester.generate_performance_report(all_results)
        
        return {
            'individual_results': individual_results,
            'composite_result': composite_result,
            'performance_report': performance_report,
            'best_strategy': performance_report.loc[
                performance_report['Total Return (%)'].idxmax(), 'Strategy'
            ] if not performance_report.empty else None
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """시스템 상태 반환"""
        strategy_summary = self.strategy_manager.get_strategy_summary()
        
        return {
            'config_type': self.config_type,
            'total_strategies': strategy_summary['total_strategies'],
            'enabled_strategies': strategy_summary['enabled_strategies'],
            'composite_config': {
                'voting_method': self.composite_strategy.config.voting_method.value,
                'min_strategies': self.composite_strategy.config.min_strategies,
                'buy_threshold': self.composite_strategy.config.buy_threshold,
                'sell_threshold': self.composite_strategy.config.sell_threshold
            },
            'strategies': strategy_summary['strategies']
        }
    
    def update_strategy_config(self, strategy_name: str, **updates):
        """전략 설정 업데이트"""
        strategy = self.strategy_manager.get_strategy(strategy_name)
        if strategy:
            for key, value in updates.items():
                if hasattr(strategy, key):
                    setattr(strategy, key, value)
                elif key in strategy.parameters:
                    strategy.parameters[key] = value
            self.logger.info(f"Updated {strategy_name} configuration")
        else:
            self.logger.warning(f"Strategy {strategy_name} not found")
    
    def enable_strategy(self, strategy_name: str):
        """전략 활성화"""
        self.strategy_manager.enable_strategy(strategy_name)
    
    def disable_strategy(self, strategy_name: str):
        """전략 비활성화"""
        self.strategy_manager.disable_strategy(strategy_name)
    
    def plot_backtest_results(self, backtest_results: Dict[str, Any], save_path: str = None):
        """백테스트 결과 시각화"""
        all_results = backtest_results['individual_results'].copy()
        if backtest_results['composite_result']:
            all_results['Composite'] = backtest_results['composite_result']
        
        # 자본 곡선 플롯
        self.tester.plot_equity_curves(all_results, save_path)
        
        # 성능 메트릭 플롯
        if save_path:
            metrics_path = save_path.replace('.png', '_metrics.png')
        else:
            metrics_path = None
        self.tester.plot_performance_metrics(all_results, metrics_path)


def create_sample_data(days: int = 1000) -> pd.DataFrame:
    """샘플 데이터 생성 (테스트용)"""
    dates = pd.date_range(start='2022-01-01', periods=days, freq='1H')
    
    # 랜덤 워크 기반 가격 생성
    np.random.seed(42)
    returns = np.random.normal(0.0001, 0.02, days)
    prices = 100 * np.exp(np.cumsum(returns))
    
    # OHLCV 데이터 생성
    df = pd.DataFrame(index=dates)
    df['close'] = prices
    df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
    df['high'] = df[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.01, days))
    df['low'] = df[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.01, days))
    df['volume'] = np.random.uniform(1000, 10000, days)
    
    return df


# 사용 예시
if __name__ == "__main__":
    # 시스템 초기화
    system = TradingSystem("default")
    
    # 샘플 데이터 생성
    sample_data = create_sample_data(2000)
    
    # 현재 분석
    analysis = system.analyze_symbol(sample_data, "BTC-USD")
    print("=== Current Analysis ===")
    print(f"Composite Signal: {analysis['composite_strategy']['signal']}")
    print(f"Confidence: {analysis['composite_strategy']['confidence']:.2f}")
    print(f"Summary: {analysis['summary']}")
    
    # 백테스트 실행
    backtest_results = system.backtest_system(sample_data, "BTC-USD")
    print("\n=== Backtest Results ===")
    print(backtest_results['performance_report'])
    
    # 시각화
    system.plot_backtest_results(backtest_results)
