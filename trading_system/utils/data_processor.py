"""
데이터 처리 유틸리티
"""
import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, List, Tuple
import logging
from datetime import datetime, timedelta


class DataProcessor:
    """데이터 처리 클래스"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def validate_ohlcv_data(df: pd.DataFrame) -> bool:
        """OHLCV 데이터 유효성 검증"""
        if df is None or df.empty:
            return False
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            return False
        
        # 가격 데이터 논리적 검증
        if not (df['high'] >= df['low']).all():
            return False
        
        if not (df['high'] >= df['open']).all() or not (df['high'] >= df['close']).all():
            return False
        
        if not (df['low'] <= df['open']).all() or not (df['low'] <= df['close']).all():
            return False
        
        # 음수 값 검증
        if (df[['open', 'high', 'low', 'close', 'volume']] < 0).any().any():
            return False
        
        return True
    
    @staticmethod
    def clean_ohlcv_data(df: pd.DataFrame) -> pd.DataFrame:
        """OHLCV 데이터 정리"""
        if df is None or df.empty:
            return df
        
        df_clean = df.copy()
        
        # 결측값 처리
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in df_clean.columns:
                # 전진 채우기 후 후진 채우기
                df_clean[col] = df_clean[col].fillna(method='ffill').fillna(method='bfill')
        
        # 이상치 제거 (가격이 0인 경우)
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in df_clean.columns:
                df_clean = df_clean[df_clean[col] > 0]
        
        # 볼륨이 음수인 경우 0으로 설정
        if 'volume' in df_clean.columns:
            df_clean['volume'] = df_clean['volume'].clip(lower=0)
        
        # 인덱스 재설정
        df_clean = df_clean.reset_index(drop=True)
        
        return df_clean
    
    @staticmethod
    def resample_data(df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """데이터 리샘플링"""
        if df is None or df.empty:
            return df
        
        try:
            df_copy = df.copy()
            
            # 인덱스가 datetime이 아닌 경우 변환
            if not isinstance(df_copy.index, pd.DatetimeIndex):
                if 'timestamp' in df_copy.columns:
                    df_copy.index = pd.to_datetime(df_copy['timestamp'])
                else:
                    df_copy.index = pd.to_datetime(df_copy.index)
            
            # 리샘플링 규칙 정의
            agg_dict = {
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }
            
            # 존재하는 컬럼만 리샘플링
            available_agg = {k: v for k, v in agg_dict.items() if k in df_copy.columns}
            
            resampled = df_copy.resample(timeframe).agg(available_agg).dropna()
            
            return resampled
            
        except Exception as e:
            logging.error(f"Error in resample_data: {e}")
            return df
    
    @staticmethod
    def calculate_returns(df: pd.DataFrame, periods: int = 1) -> pd.Series:
        """수익률 계산"""
        if df is None or df.empty or 'close' not in df.columns:
            return pd.Series()
        
        return df['close'].pct_change(periods=periods)
    
    @staticmethod
    def calculate_volatility(df: pd.DataFrame, window: int = 20) -> pd.Series:
        """변동성 계산"""
        returns = DataProcessor.calculate_returns(df)
        return returns.rolling(window=window).std() * np.sqrt(252)  # 연환산
    
    @staticmethod
    def detect_outliers(series: pd.Series, method: str = 'iqr', threshold: float = 1.5) -> pd.Series:
        """이상치 감지"""
        if method == 'iqr':
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR
            return (series < lower_bound) | (series > upper_bound)
        
        elif method == 'zscore':
            z_scores = np.abs((series - series.mean()) / series.std())
            return z_scores > threshold
        
        else:
            return pd.Series([False] * len(series), index=series.index)
    
    @staticmethod
    def normalize_data(df: pd.DataFrame, method: str = 'minmax') -> pd.DataFrame:
        """데이터 정규화"""
        if df is None or df.empty:
            return df
        
        df_norm = df.copy()
        numeric_columns = df_norm.select_dtypes(include=[np.number]).columns
        
        if method == 'minmax':
            for col in numeric_columns:
                min_val = df_norm[col].min()
                max_val = df_norm[col].max()
                if max_val != min_val:
                    df_norm[col] = (df_norm[col] - min_val) / (max_val - min_val)
        
        elif method == 'zscore':
            for col in numeric_columns:
                mean_val = df_norm[col].mean()
                std_val = df_norm[col].std()
                if std_val != 0:
                    df_norm[col] = (df_norm[col] - mean_val) / std_val
        
        return df_norm
    
    @staticmethod
    def add_time_features(df: pd.DataFrame) -> pd.DataFrame:
        """시간 기반 피처 추가"""
        if df is None or df.empty:
            return df
        
        df_time = df.copy()
        
        # 인덱스가 datetime이 아닌 경우 변환 시도
        if not isinstance(df_time.index, pd.DatetimeIndex):
            if 'timestamp' in df_time.columns:
                df_time.index = pd.to_datetime(df_time['timestamp'])
            else:
                try:
                    df_time.index = pd.to_datetime(df_time.index)
                except:
                    return df_time
        
        # 시간 피처 추가
        df_time['hour'] = df_time.index.hour
        df_time['day_of_week'] = df_time.index.dayofweek
        df_time['day_of_month'] = df_time.index.day
        df_time['month'] = df_time.index.month
        df_time['quarter'] = df_time.index.quarter
        
        # 거래 시간 구분 (암호화폐는 24시간이지만 활성 시간대 구분)
        df_time['is_asian_hours'] = ((df_time['hour'] >= 0) & (df_time['hour'] < 8)).astype(int)
        df_time['is_european_hours'] = ((df_time['hour'] >= 8) & (df_time['hour'] < 16)).astype(int)
        df_time['is_american_hours'] = ((df_time['hour'] >= 16) & (df_time['hour'] < 24)).astype(int)
        
        # 주말 여부
        df_time['is_weekend'] = (df_time['day_of_week'] >= 5).astype(int)
        
        return df_time
    
    @staticmethod
    def calculate_support_resistance(df: pd.DataFrame, window: int = 20, 
                                   min_touches: int = 2) -> Dict[str, List[float]]:
        """지지/저항선 계산"""
        if df is None or df.empty or len(df) < window:
            return {'support': [], 'resistance': []}
        
        highs = df['high'].rolling(window=window, center=True).max()
        lows = df['low'].rolling(window=window, center=True).min()
        
        # 지역 최고점과 최저점 찾기
        local_highs = df[df['high'] == highs]['high'].dropna()
        local_lows = df[df['low'] == lows]['low'].dropna()
        
        # 비슷한 수준의 가격대 그룹화
        def group_levels(levels, tolerance=0.02):
            if len(levels) == 0:
                return []
            
            levels_sorted = sorted(levels)
            groups = []
            current_group = [levels_sorted[0]]
            
            for level in levels_sorted[1:]:
                if abs(level - current_group[-1]) / current_group[-1] <= tolerance:
                    current_group.append(level)
                else:
                    if len(current_group) >= min_touches:
                        groups.append(np.mean(current_group))
                    current_group = [level]
            
            if len(current_group) >= min_touches:
                groups.append(np.mean(current_group))
            
            return groups
        
        support_levels = group_levels(local_lows.tolist())
        resistance_levels = group_levels(local_highs.tolist())
        
        return {
            'support': support_levels,
            'resistance': resistance_levels
        }
    
    @staticmethod
    def get_data_summary(df: pd.DataFrame) -> Dict[str, Any]:
        """데이터 요약 정보"""
        if df is None or df.empty:
            return {}
        
        summary = {
            'total_records': len(df),
            'date_range': {
                'start': df.index[0] if isinstance(df.index, pd.DatetimeIndex) else None,
                'end': df.index[-1] if isinstance(df.index, pd.DatetimeIndex) else None
            },
            'missing_values': df.isnull().sum().to_dict(),
            'data_types': df.dtypes.to_dict()
        }
        
        # 가격 데이터 요약
        if 'close' in df.columns:
            summary['price_summary'] = {
                'min': df['close'].min(),
                'max': df['close'].max(),
                'mean': df['close'].mean(),
                'std': df['close'].std(),
                'current': df['close'].iloc[-1]
            }
        
        # 볼륨 데이터 요약
        if 'volume' in df.columns:
            summary['volume_summary'] = {
                'min': df['volume'].min(),
                'max': df['volume'].max(),
                'mean': df['volume'].mean(),
                'std': df['volume'].std(),
                'current': df['volume'].iloc[-1]
            }
        
        return summary
