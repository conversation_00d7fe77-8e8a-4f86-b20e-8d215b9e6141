"""
기술적 지표 계산 유틸리티
"""
import pandas as pd
import numpy as np
import ta
from typing import Tuple, Optional


class TechnicalIndicators:
    """기술적 지표 계산 클래스"""
    
    @staticmethod
    def rsi(close: pd.Series, window: int = 14) -> pd.Series:
        """RSI 계산"""
        return ta.momentum.RSIIndicator(close, window=window).rsi()
    
    @staticmethod
    def macd(close: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> <PERSON><PERSON>[pd.Series, pd.Series, pd.Series]:
        """MACD 계산"""
        macd_indicator = ta.trend.MACD(close, window_fast=fast, window_slow=slow, window_sign=signal)
        return (
            macd_indicator.macd(),
            macd_indicator.macd_signal(),
            macd_indicator.macd_diff()
        )
    
    @staticmethod
    def bollinger_bands(close: pd.Series, window: int = 20, std_dev: float = 2.0) -> <PERSON><PERSON>[pd.Series, pd.Series, pd.Series]:
        """볼린저 밴드 계산"""
        bb = ta.volatility.BollingerBands(close, window=window, window_dev=std_dev)
        return (
            bb.bollinger_hband(),  # 상단
            bb.bollinger_mavg(),   # 중간
            bb.bollinger_lband()   # 하단
        )
    
    @staticmethod
    def moving_averages(close: pd.Series, windows: list = [20, 50, 100, 200]) -> dict:
        """이동평균선들 계산"""
        mas = {}
        for window in windows:
            mas[f'ma_{window}'] = close.rolling(window=window).mean()
        return mas
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, 
                  k_window: int = 14, d_window: int = 3) -> Tuple[pd.Series, pd.Series]:
        """스토캐스틱 계산"""
        stoch = ta.momentum.StochasticOscillator(high, low, close, window=k_window, smooth_window=d_window)
        return stoch.stoch(), stoch.stoch_signal()
    
    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """윌리엄스 %R 계산"""
        return ta.momentum.WilliamsRIndicator(high, low, close, lbp=window).williams_r()
    
    @staticmethod
    def cci(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 20) -> pd.Series:
        """CCI 계산"""
        return ta.trend.CCIIndicator(high, low, close, window=window).cci()
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """ATR 계산"""
        return ta.volatility.AverageTrueRange(high, low, close, window=window).average_true_range()
    
    @staticmethod
    def adx(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """ADX 계산"""
        return ta.trend.ADXIndicator(high, low, close, window=window).adx()
    
    @staticmethod
    def obv(close: pd.Series, volume: pd.Series) -> pd.Series:
        """OBV 계산"""
        return ta.volume.OnBalanceVolumeIndicator(close, volume).on_balance_volume()
    
    @staticmethod
    def pvt(close: pd.Series, volume: pd.Series) -> pd.Series:
        """PVT 계산"""
        price_change_pct = close.pct_change()
        return (price_change_pct * volume).cumsum()
    
    @staticmethod
    def ichimoku(high: pd.Series, low: pd.Series, 
                window1: int = 9, window2: int = 26, window3: int = 52) -> dict:
        """이치모쿠 지표 계산"""
        ichimoku_indicator = ta.trend.IchimokuIndicator(high, low, window1, window2, window3)
        return {
            'tenkan_sen': ichimoku_indicator.ichimoku_conversion_line(),
            'kijun_sen': ichimoku_indicator.ichimoku_base_line(),
            'senkou_span_a': ichimoku_indicator.ichimoku_a(),
            'senkou_span_b': ichimoku_indicator.ichimoku_b(),
            'chikou_span': close.shift(-window2)
        }
    
    @staticmethod
    def wavetrend(high: pd.Series, low: pd.Series, close: pd.Series,
                 channel_length: int = 10, average_length: int = 21) -> Tuple[pd.Series, pd.Series]:
        """WaveTrend 계산"""
        hlc3 = (high + low + close) / 3
        esa = ta.trend.EMAIndicator(hlc3, window=channel_length).ema_indicator()
        d = ta.trend.EMAIndicator((hlc3 - esa).abs(), window=channel_length).ema_indicator()
        ci = (hlc3 - esa) / (0.015 * d)
        tci = ta.trend.EMAIndicator(ci, window=average_length).ema_indicator()
        wt1 = tci
        wt2 = wt1.rolling(4).mean()
        return wt1, wt2
    
    @staticmethod
    def ehlers_dft_adapted_rsi(df: pd.DataFrame, window: int = 50, frac: float = 0.5) -> pd.Series:
        """Ehlers DFT 적응형 RSI 계산"""
        try:
            if len(df) < window:
                return pd.Series([50] * len(df), index=df.index)
                
            price = (df['high'] + df['low']) / 2
            cleaned_data = price.copy()

            # Detrending
            hp = price.copy()
            alpha1 = (1 - np.sin(2 * np.pi / 40)) / np.cos(2 * np.pi / 40)
            for i in range(6, len(price)):
                hp.iloc[i] = 0.5 * (1 + alpha1) * (price.iloc[i] - price.iloc[i-1]) + alpha1 * hp.iloc[i-1]
                cleaned_data.iloc[i] = (hp.iloc[i] + 2*hp.iloc[i-1] + 3*hp.iloc[i-2] +
                                        3*hp.iloc[i-3] + 2*hp.iloc[i-4] + hp.iloc[i-5]) / 12

            # DFT Power Spectrum
            powers = []
            for period in range(8, 51):
                cosine_sum, sine_sum = 0.0, 0.0
                for n in range(min(window, len(cleaned_data))):
                    angle = 2 * np.pi * n / period
                    cosine_sum += cleaned_data.iloc[-n-1] * np.cos(angle)
                    sine_sum += cleaned_data.iloc[-n-1] * np.sin(angle)
                power = cosine_sum**2 + sine_sum**2
                powers.append(power)

            max_power = max(powers) if powers else 1
            dbs = []
            for p in powers:
                if p > 0 and max_power > 0:
                    db = -10 * np.log10(0.01 / (1 - 0.99 * p / max_power))
                    dbs.append(min(db, 20))
                else:
                    dbs.append(0)

            # Dominant cycle
            numerator = 0.0
            denominator = 0.0
            for i, db in enumerate(dbs):
                if db < 3:
                    weight = 3 - db
                    period = i + 8
                    numerator += period * weight
                    denominator += weight
            dominant_cycle = numerator / denominator if denominator != 0 else 20

            # RSI Calculation
            dynamic_len = int(min(window, np.ceil(frac * dominant_cycle)))
            delta = price.diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=dynamic_len).mean()
            avg_loss = loss.rolling(window=dynamic_len).mean()
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            return rsi.fillna(50)
        except Exception as e:
            print(f"Error in ehlers_dft_adapted_rsi: {e}")
            return pd.Series([50] * len(df), index=df.index)
    
    @staticmethod
    def zscore(close: pd.Series, window: int = 20) -> pd.Series:
        """Z-Score 계산"""
        mean = close.rolling(window).mean()
        std = close.rolling(window).std()
        return (close - mean) / std
    
    @staticmethod
    def detect_double_bottom(low: pd.Series, window: int = 10, tolerance: float = 0.02) -> pd.Series:
        """더블 바텀 패턴 감지"""
        signals = pd.Series(False, index=low.index)
        
        for i in range(window, len(low) - window):
            # 현재 지점이 지역 최저점인지 확인
            current_low = low.iloc[i]
            left_window = low.iloc[i-window:i]
            right_window = low.iloc[i+1:i+window+1]
            
            if current_low <= left_window.min() and current_low <= right_window.min():
                # 이전 지역 최저점 찾기
                for j in range(i-window*2, max(0, i-window)):
                    if j < 0:
                        continue
                    prev_low = low.iloc[j]
                    if abs(current_low - prev_low) / prev_low <= tolerance:
                        signals.iloc[i] = True
                        break
        
        return signals
