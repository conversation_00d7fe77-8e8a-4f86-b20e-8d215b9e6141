"""
전략 설정 관리
"""
from typing import Dict, Any
from ..core.base_strategy import StrategyConfig
from ..core.composite_strategy import CompositeConfig, VotingMethod


class StrategyConfigs:
    """전략 설정 관리 클래스"""
    
    @staticmethod
    def get_default_configs() -> Dict[str, StrategyConfig]:
        """기본 전략 설정들 반환"""
        return {
            # RSI 전략들
            "RSI": StrategyConfig(
                name="RSI",
                enabled=True,
                weight=1.0,
                parameters={
                    'window': 14,
                    'oversold': 30,
                    'overbought': 70
                }
            ),
            
            "RSI_Histogram": StrategyConfig(
                name="RSI_Histogram",
                enabled=True,
                weight=1.2,
                parameters={
                    'rsi_window': 14,
                    'signal_window': 9
                }
            ),
            
            "RSI_Combo_Strong": StrategyConfig(
                name="RSI_Combo_Strong",
                enabled=True,
                weight=1.5,
                parameters={
                    'rsi_window': 55,
                    'signal_window': 62
                }
            ),
            
            "DFT_RSI": StrategyConfig(
                name="DFT_RSI",
                enabled=True,
                weight=1.3,
                parameters={
                    'window': 50,
                    'frac': 0.5,
                    'fast_span': 13,
                    'slow_span': 89
                }
            ),
            
            # 트렌드 전략들
            "MACD": StrategyConfig(
                name="MACD",
                enabled=True,
                weight=1.1,
                parameters={
                    'fast': 12,
                    'slow': 26,
                    'signal': 9
                }
            ),
            
            "Ichimoku": StrategyConfig(
                name="Ichimoku",
                enabled=True,
                weight=1.2,
                parameters={
                    'window1': 9,
                    'window2': 26,
                    'window3': 52
                }
            ),
            
            "Multi_Ichimoku": StrategyConfig(
                name="Multi_Ichimoku",
                enabled=True,
                weight=1.4,
                parameters={
                    'window1_1': 9,
                    'window2_1': 26,
                    'window3_1': 52,
                    'window1_2': 27,
                    'window2_2': 78,
                    'window3_2': 156
                }
            ),
            
            "WaveTrend": StrategyConfig(
                name="WaveTrend",
                enabled=True,
                weight=1.1,
                parameters={
                    'channel_length': 10,
                    'average_length': 21,
                    'resample_period': '10min'
                }
            ),
            
            # 변동성 전략들
            "Bollinger": StrategyConfig(
                name="Bollinger",
                enabled=True,
                weight=1.0,
                parameters={
                    'window': 20,
                    'std_dev': 2.0
                }
            ),
            
            "ZScore": StrategyConfig(
                name="ZScore",
                enabled=True,
                weight=0.9,
                parameters={
                    'window': 20,
                    'buy_threshold': -2.0,
                    'sell_threshold': 2.0
                }
            ),
            
            "DoubleBottom": StrategyConfig(
                name="DoubleBottom",
                enabled=True,
                weight=0.8,
                parameters={
                    'window': 10,
                    'tolerance': 0.02
                }
            ),
            
            # 볼륨 전략들
            "PVT_Cross": StrategyConfig(
                name="PVT_Cross",
                enabled=True,
                weight=1.1,
                parameters={
                    'ma_window': 20
                }
            ),
            
            "OBV_Trend": StrategyConfig(
                name="OBV_Trend",
                enabled=True,
                weight=1.0,
                parameters={
                    'short_ma': 10,
                    'long_ma': 30
                }
            ),
            
            "Volume_Breakout": StrategyConfig(
                name="Volume_Breakout",
                enabled=True,
                weight=1.2,
                parameters={
                    'volume_ma_window': 20,
                    'volume_multiplier': 2.0,
                    'price_change_threshold': 0.02
                }
            )
        }
    
    @staticmethod
    def get_conservative_configs() -> Dict[str, StrategyConfig]:
        """보수적 전략 설정들 반환"""
        configs = StrategyConfigs.get_default_configs()
        
        # RSI 임계값을 더 보수적으로 설정
        configs["RSI"].parameters.update({
            'oversold': 25,
            'overbought': 75
        })
        
        # 볼린저 밴드 표준편차 증가
        configs["Bollinger"].parameters.update({
            'std_dev': 2.5
        })
        
        # Z-Score 임계값 증가
        configs["ZScore"].parameters.update({
            'buy_threshold': -2.5,
            'sell_threshold': 2.5
        })
        
        return configs
    
    @staticmethod
    def get_aggressive_configs() -> Dict[str, StrategyConfig]:
        """공격적 전략 설정들 반환"""
        configs = StrategyConfigs.get_default_configs()
        
        # RSI 임계값을 더 공격적으로 설정
        configs["RSI"].parameters.update({
            'oversold': 35,
            'overbought': 65
        })
        
        # 볼린저 밴드 표준편차 감소
        configs["Bollinger"].parameters.update({
            'std_dev': 1.5
        })
        
        # Z-Score 임계값 감소
        configs["ZScore"].parameters.update({
            'buy_threshold': -1.5,
            'sell_threshold': 1.5
        })
        
        # 볼륨 브레이크아웃 민감도 증가
        configs["Volume_Breakout"].parameters.update({
            'volume_multiplier': 1.5,
            'price_change_threshold': 0.015
        })
        
        return configs
    
    @staticmethod
    def get_composite_configs() -> Dict[str, CompositeConfig]:
        """복합전략 설정들 반환"""
        return {
            "default": CompositeConfig(
                voting_method=VotingMethod.CONFIDENCE_WEIGHTED,
                min_strategies=3,
                buy_threshold=0.6,
                sell_threshold=0.6,
                strong_signal_threshold=0.8,
                ignore_errors=True,
                require_volume_confirmation=False
            ),
            
            "conservative": CompositeConfig(
                voting_method=VotingMethod.CONFIDENCE_WEIGHTED,
                min_strategies=5,
                buy_threshold=0.7,
                sell_threshold=0.7,
                strong_signal_threshold=0.85,
                ignore_errors=True,
                require_volume_confirmation=True
            ),
            
            "aggressive": CompositeConfig(
                voting_method=VotingMethod.WEIGHTED_AVERAGE,
                min_strategies=2,
                buy_threshold=0.5,
                sell_threshold=0.5,
                strong_signal_threshold=0.75,
                ignore_errors=True,
                require_volume_confirmation=False
            ),
            
            "unanimous": CompositeConfig(
                voting_method=VotingMethod.UNANIMOUS,
                min_strategies=3,
                buy_threshold=1.0,
                sell_threshold=1.0,
                strong_signal_threshold=1.0,
                ignore_errors=True,
                require_volume_confirmation=False
            ),
            
            "majority": CompositeConfig(
                voting_method=VotingMethod.SIMPLE_MAJORITY,
                min_strategies=3,
                buy_threshold=0.5,
                sell_threshold=0.5,
                strong_signal_threshold=0.8,
                ignore_errors=True,
                require_volume_confirmation=False
            )
        }
    
    @staticmethod
    def create_custom_config(name: str, enabled: bool = True, weight: float = 1.0, 
                           **parameters) -> StrategyConfig:
        """커스텀 전략 설정 생성"""
        return StrategyConfig(
            name=name,
            enabled=enabled,
            weight=weight,
            parameters=parameters
        )
    
    @staticmethod
    def update_config(config: StrategyConfig, **updates) -> StrategyConfig:
        """기존 설정 업데이트"""
        new_config = StrategyConfig(
            name=config.name,
            enabled=updates.get('enabled', config.enabled),
            weight=updates.get('weight', config.weight),
            parameters=config.parameters.copy()
        )
        
        if 'parameters' in updates:
            new_config.parameters.update(updates['parameters'])
        
        return new_config
