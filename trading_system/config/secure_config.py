"""
보안 설정 관리 시스템
"""
import os
import json
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from typing import Dict, Optional, Any
import logging
from pathlib import Path


class SecureConfig:
    """보안 설정 관리 클래스"""
    
    def __init__(self, config_dir: str = ".secure_config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # 암호화 키 파일 경로
        self.key_file = self.config_dir / "master.key"
        self.config_file = self.config_dir / "encrypted_config.json"
        
        # 마스터 키 로드 또는 생성
        self._load_or_create_master_key()
    
    def _load_or_create_master_key(self):
        """마스터 키 로드 또는 생성"""
        if self.key_file.exists():
            with open(self.key_file, 'rb') as f:
                self.master_key = f.read()
        else:
            # 새 마스터 키 생성
            self.master_key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(self.master_key)
            
            # 파일 권한 설정 (Unix 계열)
            try:
                os.chmod(self.key_file, 0o600)  # 소유자만 읽기/쓰기
            except:
                pass
        
        self.cipher = Fernet(self.master_key)
    
    def encrypt_data(self, data: str) -> str:
        """데이터 암호화"""
        encrypted = self.cipher.encrypt(data.encode())
        return base64.b64encode(encrypted).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """데이터 복호화"""
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted = self.cipher.decrypt(encrypted_bytes)
        return decrypted.decode()
    
    def save_api_credentials(self, exchange: str, api_key: str, api_secret: str, 
                           additional_data: Dict[str, Any] = None):
        """API 자격증명 저장"""
        # 기존 설정 로드
        config = self._load_encrypted_config()
        
        # 새 자격증명 추가
        credentials = {
            'api_key': self.encrypt_data(api_key),
            'api_secret': self.encrypt_data(api_secret)
        }
        
        if additional_data:
            for key, value in additional_data.items():
                credentials[key] = self.encrypt_data(str(value))
        
        config[exchange] = credentials
        
        # 암호화된 설정 저장
        self._save_encrypted_config(config)
        self.logger.info(f"API credentials saved for {exchange}")
    
    def get_api_credentials(self, exchange: str) -> Optional[Dict[str, str]]:
        """API 자격증명 조회"""
        config = self._load_encrypted_config()
        
        if exchange not in config:
            return None
        
        encrypted_creds = config[exchange]
        decrypted_creds = {}
        
        for key, encrypted_value in encrypted_creds.items():
            try:
                decrypted_creds[key] = self.decrypt_data(encrypted_value)
            except Exception as e:
                self.logger.error(f"Failed to decrypt {key} for {exchange}: {e}")
                return None
        
        return decrypted_creds
    
    def _load_encrypted_config(self) -> Dict[str, Any]:
        """암호화된 설정 로드"""
        if not self.config_file.exists():
            return {}
        
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            return {}
    
    def _save_encrypted_config(self, config: Dict[str, Any]):
        """암호화된 설정 저장"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            # 파일 권한 설정
            try:
                os.chmod(self.config_file, 0o600)
            except:
                pass
                
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")
    
    def list_exchanges(self) -> list:
        """저장된 거래소 목록 반환"""
        config = self._load_encrypted_config()
        return list(config.keys())
    
    def remove_credentials(self, exchange: str) -> bool:
        """자격증명 삭제"""
        config = self._load_encrypted_config()
        
        if exchange in config:
            del config[exchange]
            self._save_encrypted_config(config)
            self.logger.info(f"Credentials removed for {exchange}")
            return True
        
        return False
    
    def export_backup(self, backup_path: str, password: str):
        """설정 백업 (비밀번호로 보호)"""
        config = self._load_encrypted_config()
        
        # 비밀번호 기반 키 생성
        password_key = base64.urlsafe_b64encode(password.encode().ljust(32)[:32])
        backup_cipher = Fernet(password_key)
        
        # 전체 설정 암호화
        config_json = json.dumps(config)
        encrypted_backup = backup_cipher.encrypt(config_json.encode())
        
        with open(backup_path, 'wb') as f:
            f.write(encrypted_backup)
        
        self.logger.info(f"Backup created: {backup_path}")
    
    def import_backup(self, backup_path: str, password: str):
        """백업에서 설정 복원"""
        try:
            # 비밀번호 기반 키 생성
            password_key = base64.urlsafe_b64encode(password.encode().ljust(32)[:32])
            backup_cipher = Fernet(password_key)
            
            # 백업 파일 복호화
            with open(backup_path, 'rb') as f:
                encrypted_backup = f.read()
            
            decrypted_config = backup_cipher.decrypt(encrypted_backup)
            config = json.loads(decrypted_config.decode())
            
            # 설정 저장
            self._save_encrypted_config(config)
            self.logger.info("Backup restored successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to restore backup: {e}")
            raise


class EnvironmentConfig:
    """환경변수 기반 설정 관리"""
    
    @staticmethod
    def load_from_env() -> Dict[str, Dict[str, str]]:
        """환경변수에서 API 키 로드"""
        config = {}
        
        # 업비트 설정
        upbit_key = os.getenv('UPBIT_ACCESS_KEY')
        upbit_secret = os.getenv('UPBIT_SECRET_KEY')
        if upbit_key and upbit_secret:
            config['upbit'] = {
                'api_key': upbit_key,
                'api_secret': upbit_secret
            }
        
        # 바이비트 설정
        bybit_key = os.getenv('BYBIT_API_KEY')
        bybit_secret = os.getenv('BYBIT_API_SECRET')
        if bybit_key and bybit_secret:
            config['bybit'] = {
                'api_key': bybit_key,
                'api_secret': bybit_secret
            }
        
        # 바이낸스 설정
        binance_key = os.getenv('BINANCE_API_KEY')
        binance_secret = os.getenv('BINANCE_API_SECRET')
        if binance_key and binance_secret:
            config['binance'] = {
                'api_key': binance_key,
                'api_secret': binance_secret
            }
        
        return config
    
    @staticmethod
    def create_env_template(file_path: str = ".env.template"):
        """환경변수 템플릿 파일 생성"""
        template = """# 트레이딩 시스템 환경변수 설정
# 실제 사용 시 .env 파일로 복사하고 실제 값으로 변경하세요

# 업비트 API 키
UPBIT_ACCESS_KEY=your_upbit_access_key_here
UPBIT_SECRET_KEY=your_upbit_secret_key_here

# 바이비트 API 키
BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_API_SECRET=your_bybit_api_secret_here

# 바이낸스 API 키 (선택사항)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

# 거래 설정
LIVE_TRADING_ENABLED=false
TRADE_QUANTITY_USDT=10
TRADE_INTERVAL_SECONDS=60

# 로깅 레벨
LOG_LEVEL=INFO
"""
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(template)
        
        print(f"환경변수 템플릿 생성: {file_path}")


def setup_secure_config():
    """보안 설정 초기 설정"""
    print("🔒 보안 설정 시스템 초기화")
    
    # SecureConfig 인스턴스 생성
    secure_config = SecureConfig()
    
    print("\n현재 저장된 거래소:")
    exchanges = secure_config.list_exchanges()
    if exchanges:
        for exchange in exchanges:
            print(f"  - {exchange}")
    else:
        print("  저장된 거래소가 없습니다.")
    
    # 환경변수 템플릿 생성
    EnvironmentConfig.create_env_template()
    
    print("\n📝 다음 단계:")
    print("1. .env.template을 .env로 복사")
    print("2. .env 파일에 실제 API 키 입력")
    print("3. .gitignore에 .env 추가")
    print("4. 기존 평문 API 키 파일들 삭제")
    
    return secure_config


if __name__ == "__main__":
    # 보안 설정 데모
    secure_config = setup_secure_config()
    
    # 예시: API 키 저장 (실제로는 사용자 입력 받아야 함)
    print("\n⚠️  데모용 - 실제 사용 시 실제 API 키를 입력하세요")
    
    # 환경변수에서 로드 시도
    env_config = EnvironmentConfig.load_from_env()
    if env_config:
        print("환경변수에서 API 키 발견:")
        for exchange, creds in env_config.items():
            print(f"  - {exchange}: API 키 로드됨")
            # 보안 저장소에 저장
            secure_config.save_api_credentials(
                exchange, 
                creds['api_key'], 
                creds['api_secret']
            )
    else:
        print("환경변수에서 API 키를 찾을 수 없습니다.")
