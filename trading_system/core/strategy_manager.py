"""
전략 매니저 및 팩토리 클래스
"""
from typing import Dict, List, Optional, Type, Any
import pandas as pd
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from .base_strategy import BaseStrategy, StrategySignal, StrategyConfig, SignalType


class StrategyFactory:
    """전략 팩토리 클래스"""
    
    _strategies: Dict[str, Type[BaseStrategy]] = {}
    
    @classmethod
    def register_strategy(cls, name: str, strategy_class: Type[BaseStrategy]):
        """전략 클래스 등록"""
        cls._strategies[name] = strategy_class
    
    @classmethod
    def create_strategy(cls, name: str, config: StrategyConfig) -> BaseStrategy:
        """전략 인스턴스 생성"""
        if name not in cls._strategies:
            raise ValueError(f"Unknown strategy: {name}")
        
        strategy_class = cls._strategies[name]
        return strategy_class(config)
    
    @classmethod
    def get_available_strategies(cls) -> List[str]:
        """사용 가능한 전략 목록 반환"""
        return list(cls._strategies.keys())


class StrategyManager:
    """전략 매니저 클래스"""
    
    def __init__(self, strategies: List[BaseStrategy] = None):
        self.strategies: Dict[str, BaseStrategy] = {}
        self.logger = logging.getLogger(__name__)
        
        if strategies:
            for strategy in strategies:
                self.add_strategy(strategy)
    
    def add_strategy(self, strategy: BaseStrategy):
        """전략 추가"""
        self.strategies[strategy.name] = strategy
        self.logger.info(f"Strategy added: {strategy.name}")
    
    def remove_strategy(self, name: str):
        """전략 제거"""
        if name in self.strategies:
            del self.strategies[name]
            self.logger.info(f"Strategy removed: {name}")
    
    def enable_strategy(self, name: str):
        """전략 활성화"""
        if name in self.strategies:
            self.strategies[name].enabled = True
            self.logger.info(f"Strategy enabled: {name}")
    
    def disable_strategy(self, name: str):
        """전략 비활성화"""
        if name in self.strategies:
            self.strategies[name].enabled = False
            self.logger.info(f"Strategy disabled: {name}")
    
    def get_strategy(self, name: str) -> Optional[BaseStrategy]:
        """전략 반환"""
        return self.strategies.get(name)
    
    def get_enabled_strategies(self) -> Dict[str, BaseStrategy]:
        """활성화된 전략들 반환"""
        return {name: strategy for name, strategy in self.strategies.items() 
                if strategy.enabled}
    
    def execute_single_strategy(self, strategy_name: str, df: pd.DataFrame, 
                              symbol: str = None) -> StrategySignal:
        """단일 전략 실행"""
        if strategy_name not in self.strategies:
            raise ValueError(f"Strategy not found: {strategy_name}")
        
        strategy = self.strategies[strategy_name]
        return strategy.execute(df, symbol)
    
    def execute_all_strategies(self, df: pd.DataFrame, symbol: str = None, 
                             parallel: bool = True) -> Dict[str, StrategySignal]:
        """모든 활성화된 전략 실행"""
        enabled_strategies = self.get_enabled_strategies()
        results = {}
        
        if not enabled_strategies:
            self.logger.warning("No enabled strategies found")
            return results
        
        if parallel and len(enabled_strategies) > 1:
            # 병렬 실행
            with ThreadPoolExecutor(max_workers=min(len(enabled_strategies), 4)) as executor:
                future_to_strategy = {
                    executor.submit(strategy.execute, df, symbol): name
                    for name, strategy in enabled_strategies.items()
                }
                
                for future in as_completed(future_to_strategy):
                    strategy_name = future_to_strategy[future]
                    try:
                        signal = future.result(timeout=30)  # 30초 타임아웃
                        results[strategy_name] = signal
                    except Exception as e:
                        self.logger.error(f"Strategy {strategy_name} failed: {e}")
                        results[strategy_name] = StrategySignal(
                            signal=SignalType.ERROR,
                            confidence=0.0,
                            timestamp=pd.Timestamp.now(),
                            price=df['close'].iloc[-1] if not df.empty else 0.0,
                            metadata={'error': str(e)}
                        )
        else:
            # 순차 실행
            for name, strategy in enabled_strategies.items():
                try:
                    signal = strategy.execute(df, symbol)
                    results[name] = signal
                except Exception as e:
                    self.logger.error(f"Strategy {name} failed: {e}")
                    results[name] = StrategySignal(
                        signal=SignalType.ERROR,
                        confidence=0.0,
                        timestamp=pd.Timestamp.now(),
                        price=df['close'].iloc[-1] if not df.empty else 0.0,
                        metadata={'error': str(e)}
                    )
        
        return results
    
    def get_strategy_summary(self) -> Dict[str, Any]:
        """전략 요약 정보 반환"""
        summary = {
            'total_strategies': len(self.strategies),
            'enabled_strategies': len(self.get_enabled_strategies()),
            'strategies': {}
        }
        
        for name, strategy in self.strategies.items():
            summary['strategies'][name] = strategy.get_strategy_info()
        
        return summary
    
    def validate_all_strategies(self, df: pd.DataFrame) -> Dict[str, bool]:
        """모든 전략의 데이터 유효성 검증"""
        results = {}
        for name, strategy in self.strategies.items():
            results[name] = strategy.validate_data(df)
        return results
    
    def get_required_periods(self) -> int:
        """모든 전략에 필요한 최대 데이터 기간 반환"""
        if not self.strategies:
            return 0
        
        return max(strategy.get_required_periods() 
                  for strategy in self.strategies.values())
    
    def reset_all_histories(self):
        """모든 전략의 신호 히스토리 초기화"""
        for strategy in self.strategies.values():
            strategy._signal_history.clear()
            strategy._last_signal = None
        self.logger.info("All strategy histories reset")


def strategy_decorator(name: str):
    """전략 클래스 데코레이터"""
    def decorator(cls):
        StrategyFactory.register_strategy(name, cls)
        return cls
    return decorator
