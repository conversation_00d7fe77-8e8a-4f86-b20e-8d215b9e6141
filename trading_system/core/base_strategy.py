"""
기본 전략 인터페이스 및 추상 클래스
"""
from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np


class SignalType(Enum):
    """매매 신호 타입"""
    STRONG_BUY = "StrongBuy"
    BUY = "Buy"
    HOLD = "Hold"
    SELL = "Sell"
    STRONG_SELL = "StrongSell"
    ERROR = "Error"


@dataclass
class StrategySignal:
    """전략 신호 데이터 클래스"""
    signal: SignalType
    confidence: float  # 신호 신뢰도 (0.0 ~ 1.0)
    timestamp: pd.Timestamp
    price: float
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class StrategyConfig:
    """전략 설정 데이터 클래스"""
    name: str
    enabled: bool = True
    weight: float = 1.0  # 복합 전략에서의 가중치
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}


class BaseStrategy(ABC):
    """기본 전략 추상 클래스"""
    
    def __init__(self, config: StrategyConfig):
        self.config = config
        self.name = config.name
        self.enabled = config.enabled
        self.weight = config.weight
        self.parameters = config.parameters
        self._last_signal: Optional[StrategySignal] = None
        self._signal_history: List[StrategySignal] = []
    
    @abstractmethod
    def calculate_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """
        매매 신호 계산
        
        Args:
            df: OHLCV 데이터프레임
            symbol: 심볼명 (선택사항)
            
        Returns:
            StrategySignal: 계산된 매매 신호
        """
        pass
    
    @abstractmethod
    def get_required_periods(self) -> int:
        """
        전략 실행에 필요한 최소 데이터 기간
        
        Returns:
            int: 필요한 최소 캔들 수
        """
        pass
    
    def validate_data(self, df: pd.DataFrame) -> bool:
        """
        데이터 유효성 검증
        
        Args:
            df: 검증할 데이터프레임
            
        Returns:
            bool: 데이터 유효성 여부
        """
        if df is None or df.empty:
            return False
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            return False
        
        if len(df) < self.get_required_periods():
            return False
        
        return True
    
    def get_last_signal(self) -> Optional[StrategySignal]:
        """마지막 신호 반환"""
        return self._last_signal
    
    def get_signal_history(self, limit: int = None) -> List[StrategySignal]:
        """신호 히스토리 반환"""
        if limit is None:
            return self._signal_history.copy()
        return self._signal_history[-limit:].copy()
    
    def _save_signal(self, signal: StrategySignal):
        """신호 저장"""
        self._last_signal = signal
        self._signal_history.append(signal)
        
        # 히스토리 크기 제한 (메모리 관리)
        max_history = 1000
        if len(self._signal_history) > max_history:
            self._signal_history = self._signal_history[-max_history:]
    
    def execute(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """
        전략 실행 (메인 진입점)
        
        Args:
            df: OHLCV 데이터프레임
            symbol: 심볼명
            
        Returns:
            StrategySignal: 계산된 매매 신호
        """
        try:
            if not self.enabled:
                signal = StrategySignal(
                    signal=SignalType.HOLD,
                    confidence=0.0,
                    timestamp=pd.Timestamp.now(),
                    price=df['close'].iloc[-1] if not df.empty else 0.0,
                    metadata={'reason': 'strategy_disabled'}
                )
                self._save_signal(signal)
                return signal
            
            if not self.validate_data(df):
                signal = StrategySignal(
                    signal=SignalType.ERROR,
                    confidence=0.0,
                    timestamp=pd.Timestamp.now(),
                    price=df['close'].iloc[-1] if not df.empty else 0.0,
                    metadata={'reason': 'invalid_data'}
                )
                self._save_signal(signal)
                return signal
            
            signal = self.calculate_signal(df, symbol)
            self._save_signal(signal)
            return signal
            
        except Exception as e:
            error_signal = StrategySignal(
                signal=SignalType.ERROR,
                confidence=0.0,
                timestamp=pd.Timestamp.now(),
                price=df['close'].iloc[-1] if not df.empty else 0.0,
                metadata={'error': str(e), 'strategy': self.name}
            )
            self._save_signal(error_signal)
            return error_signal
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """전략 정보 반환"""
        return {
            'name': self.name,
            'enabled': self.enabled,
            'weight': self.weight,
            'parameters': self.parameters,
            'required_periods': self.get_required_periods(),
            'last_signal': self._last_signal.signal.value if self._last_signal else None,
            'signal_count': len(self._signal_history)
        }


class StrategyError(Exception):
    """전략 관련 예외 클래스"""
    pass


class InsufficientDataError(StrategyError):
    """데이터 부족 예외"""
    pass


class InvalidParameterError(StrategyError):
    """잘못된 파라미터 예외"""
    pass
