"""
복합전략 시스템
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from enum import Enum
from dataclasses import dataclass
import logging

from .base_strategy import BaseStrategy, StrategySignal, StrategyConfig, SignalType
from .strategy_manager import StrategyManager


class VotingMethod(Enum):
    """투표 방식"""
    SIMPLE_MAJORITY = "simple_majority"  # 단순 다수결
    WEIGHTED_AVERAGE = "weighted_average"  # 가중 평균
    CONFIDENCE_WEIGHTED = "confidence_weighted"  # 신뢰도 가중
    UNANIMOUS = "unanimous"  # 만장일치
    THRESHOLD_BASED = "threshold_based"  # 임계값 기반


@dataclass
class CompositeConfig:
    """복합전략 설정"""
    voting_method: VotingMethod = VotingMethod.CONFIDENCE_WEIGHTED
    min_strategies: int = 3  # 최소 전략 수
    buy_threshold: float = 0.6  # 매수 임계값
    sell_threshold: float = 0.6  # 매도 임계값
    strong_signal_threshold: float = 0.8  # 강한 신호 임계값
    ignore_errors: bool = True  # 에러 신호 무시 여부
    require_volume_confirmation: bool = False  # 볼륨 확인 필요 여부


class CompositeStrategy:
    """복합전략 클래스"""
    
    def __init__(self, strategy_manager: StrategyManager, config: CompositeConfig = None):
        self.strategy_manager = strategy_manager
        self.config = config or CompositeConfig()
        self.logger = logging.getLogger(__name__)
        self._last_composite_signal: Optional[StrategySignal] = None
        self._signal_history: List[Dict[str, Any]] = []
    
    def calculate_composite_signal(self, df: pd.DataFrame, symbol: str = None) -> StrategySignal:
        """복합 신호 계산"""
        # 모든 전략 실행
        strategy_signals = self.strategy_manager.execute_all_strategies(df, symbol)
        
        if not strategy_signals:
            return self._create_error_signal(df, "no_strategies_available")
        
        # 에러 신호 필터링
        if self.config.ignore_errors:
            strategy_signals = {
                name: signal for name, signal in strategy_signals.items()
                if signal.signal != SignalType.ERROR
            }
        
        # 최소 전략 수 확인
        if len(strategy_signals) < self.config.min_strategies:
            return self._create_error_signal(df, "insufficient_strategies")
        
        # 투표 방식에 따른 신호 계산
        composite_signal = self._calculate_by_voting_method(strategy_signals, df)
        
        # 히스토리 저장
        self._save_composite_history(strategy_signals, composite_signal)
        
        return composite_signal
    
    def _calculate_by_voting_method(self, strategy_signals: Dict[str, StrategySignal], 
                                  df: pd.DataFrame) -> StrategySignal:
        """투표 방식에 따른 신호 계산"""
        current_price = df['close'].iloc[-1]
        
        if self.config.voting_method == VotingMethod.SIMPLE_MAJORITY:
            return self._simple_majority_vote(strategy_signals, current_price)
        elif self.config.voting_method == VotingMethod.WEIGHTED_AVERAGE:
            return self._weighted_average_vote(strategy_signals, current_price)
        elif self.config.voting_method == VotingMethod.CONFIDENCE_WEIGHTED:
            return self._confidence_weighted_vote(strategy_signals, current_price)
        elif self.config.voting_method == VotingMethod.UNANIMOUS:
            return self._unanimous_vote(strategy_signals, current_price)
        elif self.config.voting_method == VotingMethod.THRESHOLD_BASED:
            return self._threshold_based_vote(strategy_signals, current_price)
        else:
            return self._confidence_weighted_vote(strategy_signals, current_price)
    
    def _simple_majority_vote(self, strategy_signals: Dict[str, StrategySignal], 
                            current_price: float) -> StrategySignal:
        """단순 다수결 투표"""
        buy_count = sum(1 for signal in strategy_signals.values() 
                       if signal.signal in [SignalType.BUY, SignalType.STRONG_BUY])
        sell_count = sum(1 for signal in strategy_signals.values() 
                        if signal.signal in [SignalType.SELL, SignalType.STRONG_SELL])
        hold_count = sum(1 for signal in strategy_signals.values() 
                        if signal.signal == SignalType.HOLD)
        
        total_count = len(strategy_signals)
        
        if buy_count > sell_count and buy_count > hold_count:
            signal_type = SignalType.BUY
            confidence = buy_count / total_count
        elif sell_count > buy_count and sell_count > hold_count:
            signal_type = SignalType.SELL
            confidence = sell_count / total_count
        else:
            signal_type = SignalType.HOLD
            confidence = hold_count / total_count
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'voting_method': 'simple_majority',
                'buy_count': buy_count,
                'sell_count': sell_count,
                'hold_count': hold_count,
                'total_strategies': total_count
            }
        )
    
    def _confidence_weighted_vote(self, strategy_signals: Dict[str, StrategySignal], 
                                current_price: float) -> StrategySignal:
        """신뢰도 가중 투표"""
        buy_weight = 0.0
        sell_weight = 0.0
        hold_weight = 0.0
        total_weight = 0.0
        
        for name, signal in strategy_signals.items():
            strategy = self.strategy_manager.get_strategy(name)
            weight = strategy.weight if strategy else 1.0
            confidence_weight = signal.confidence * weight
            
            if signal.signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                buy_weight += confidence_weight
            elif signal.signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                sell_weight += confidence_weight
            else:  # HOLD
                hold_weight += confidence_weight
            
            total_weight += weight
        
        # 정규화
        if total_weight > 0:
            buy_score = buy_weight / total_weight
            sell_score = sell_weight / total_weight
            hold_score = hold_weight / total_weight
        else:
            buy_score = sell_score = hold_score = 0.0
        
        # 신호 결정
        if buy_score >= self.config.buy_threshold and buy_score > sell_score:
            if buy_score >= self.config.strong_signal_threshold:
                signal_type = SignalType.STRONG_BUY
            else:
                signal_type = SignalType.BUY
            confidence = buy_score
        elif sell_score >= self.config.sell_threshold and sell_score > buy_score:
            if sell_score >= self.config.strong_signal_threshold:
                signal_type = SignalType.STRONG_SELL
            else:
                signal_type = SignalType.SELL
            confidence = sell_score
        else:
            signal_type = SignalType.HOLD
            confidence = max(buy_score, sell_score, hold_score)
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'voting_method': 'confidence_weighted',
                'buy_score': buy_score,
                'sell_score': sell_score,
                'hold_score': hold_score,
                'total_weight': total_weight
            }
        )
    
    def _weighted_average_vote(self, strategy_signals: Dict[str, StrategySignal], 
                             current_price: float) -> StrategySignal:
        """가중 평균 투표"""
        signal_values = {
            SignalType.STRONG_SELL: -2,
            SignalType.SELL: -1,
            SignalType.HOLD: 0,
            SignalType.BUY: 1,
            SignalType.STRONG_BUY: 2
        }
        
        weighted_sum = 0.0
        total_weight = 0.0
        
        for name, signal in strategy_signals.items():
            strategy = self.strategy_manager.get_strategy(name)
            weight = strategy.weight if strategy else 1.0
            signal_value = signal_values.get(signal.signal, 0)
            
            weighted_sum += signal_value * weight * signal.confidence
            total_weight += weight
        
        if total_weight > 0:
            average_signal = weighted_sum / total_weight
        else:
            average_signal = 0.0
        
        # 신호 변환
        if average_signal >= 1.5:
            signal_type = SignalType.STRONG_BUY
        elif average_signal >= 0.5:
            signal_type = SignalType.BUY
        elif average_signal <= -1.5:
            signal_type = SignalType.STRONG_SELL
        elif average_signal <= -0.5:
            signal_type = SignalType.SELL
        else:
            signal_type = SignalType.HOLD
        
        confidence = min(1.0, abs(average_signal) / 2.0)
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'voting_method': 'weighted_average',
                'average_signal': average_signal,
                'total_weight': total_weight
            }
        )
    
    def _unanimous_vote(self, strategy_signals: Dict[str, StrategySignal], 
                       current_price: float) -> StrategySignal:
        """만장일치 투표"""
        signals = [signal.signal for signal in strategy_signals.values()]
        unique_signals = set(signals)
        
        if len(unique_signals) == 1:
            signal_type = signals[0]
            confidence = np.mean([signal.confidence for signal in strategy_signals.values()])
        else:
            signal_type = SignalType.HOLD
            confidence = 0.2
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'voting_method': 'unanimous',
                'unanimous': len(unique_signals) == 1,
                'unique_signals': len(unique_signals)
            }
        )
    
    def _threshold_based_vote(self, strategy_signals: Dict[str, StrategySignal], 
                            current_price: float) -> StrategySignal:
        """임계값 기반 투표"""
        buy_signals = [s for s in strategy_signals.values() 
                      if s.signal in [SignalType.BUY, SignalType.STRONG_BUY]]
        sell_signals = [s for s in strategy_signals.values() 
                       if s.signal in [SignalType.SELL, SignalType.STRONG_SELL]]
        
        total_strategies = len(strategy_signals)
        buy_ratio = len(buy_signals) / total_strategies
        sell_ratio = len(sell_signals) / total_strategies
        
        if buy_ratio >= self.config.buy_threshold:
            signal_type = SignalType.BUY
            confidence = buy_ratio
        elif sell_ratio >= self.config.sell_threshold:
            signal_type = SignalType.SELL
            confidence = sell_ratio
        else:
            signal_type = SignalType.HOLD
            confidence = 1.0 - max(buy_ratio, sell_ratio)
        
        return StrategySignal(
            signal=signal_type,
            confidence=confidence,
            timestamp=pd.Timestamp.now(),
            price=current_price,
            metadata={
                'voting_method': 'threshold_based',
                'buy_ratio': buy_ratio,
                'sell_ratio': sell_ratio,
                'buy_threshold': self.config.buy_threshold,
                'sell_threshold': self.config.sell_threshold
            }
        )
    
    def _create_error_signal(self, df: pd.DataFrame, reason: str) -> StrategySignal:
        """에러 신호 생성"""
        return StrategySignal(
            signal=SignalType.ERROR,
            confidence=0.0,
            timestamp=pd.Timestamp.now(),
            price=df['close'].iloc[-1] if not df.empty else 0.0,
            metadata={'error_reason': reason}
        )
    
    def _save_composite_history(self, strategy_signals: Dict[str, StrategySignal], 
                              composite_signal: StrategySignal):
        """복합 신호 히스토리 저장"""
        history_entry = {
            'timestamp': pd.Timestamp.now(),
            'composite_signal': composite_signal,
            'strategy_signals': strategy_signals.copy(),
            'strategy_count': len(strategy_signals)
        }
        
        self._signal_history.append(history_entry)
        self._last_composite_signal = composite_signal
        
        # 히스토리 크기 제한
        max_history = 1000
        if len(self._signal_history) > max_history:
            self._signal_history = self._signal_history[-max_history:]
    
    def get_last_composite_signal(self) -> Optional[StrategySignal]:
        """마지막 복합 신호 반환"""
        return self._last_composite_signal
    
    def get_signal_summary(self) -> Dict[str, Any]:
        """신호 요약 정보 반환"""
        if not self._signal_history:
            return {}
        
        last_entry = self._signal_history[-1]
        strategy_signals = last_entry['strategy_signals']
        
        buy_count = sum(1 for s in strategy_signals.values() 
                       if s.signal in [SignalType.BUY, SignalType.STRONG_BUY])
        sell_count = sum(1 for s in strategy_signals.values() 
                        if s.signal in [SignalType.SELL, SignalType.STRONG_SELL])
        hold_count = sum(1 for s in strategy_signals.values() 
                        if s.signal == SignalType.HOLD)
        error_count = sum(1 for s in strategy_signals.values() 
                         if s.signal == SignalType.ERROR)
        
        total = len(strategy_signals)
        
        return {
            'composite_signal': last_entry['composite_signal'].signal.value,
            'composite_confidence': last_entry['composite_signal'].confidence,
            'total_strategies': total,
            'buy_signals': buy_count,
            'sell_signals': sell_count,
            'hold_signals': hold_count,
            'error_signals': error_count,
            'buy_percentage': (buy_count / total * 100) if total > 0 else 0,
            'sell_percentage': (sell_count / total * 100) if total > 0 else 0,
            'voting_method': self.config.voting_method.value,
            'timestamp': last_entry['timestamp']
        }
