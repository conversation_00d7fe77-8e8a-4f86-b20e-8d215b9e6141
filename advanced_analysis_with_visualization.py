"""
고급 분석 + 시각화 통합 시스템
- main5의 SMC, VPVR, 거래량 분석
- main6의 시각화 및 실행 속도
- 업비트 매매 연동 가능
"""
import pandas as pd
import numpy as np
import requests
import time
import os
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 한글 폰트 설정
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

class AdvancedCryptoAnalyzer:
    """고급 암호화폐 분석기"""
    
    def __init__(self, save_dir="analysis_results"):
        self.save_dir = save_dir
        self.create_directory()
        
        # 분석 설정
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT']
        self.periods = {
            '5일': 5 * 24,    # 5일 = 120시간 (60분봉 기준)
            '20일': 20 * 24,  # 20일 = 480시간
            '50일': 50 * 24,  # 50일 = 1200시간
            '100일': 100 * 24 # 100일 = 2400시간
        }
    
    def create_directory(self):
        """결과 저장 디렉토리 생성"""
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
    
    def fetch_bybit_data(self, symbol, interval=60, hours=2400):
        """Bybit 데이터 수집 (최적화된 버전)"""
        url = "https://api.bybit.com/v5/market/kline"
        data_list = []
        
        end_time = int(time.time() * 1000)
        start_time = end_time - (hours * 60 * 60 * 1000)
        
        print(f"  📊 {symbol} 데이터 수집 중... (최근 {hours//24}일)")
        
        while start_time < end_time and len(data_list) < hours:
            params = {
                "category": "linear",
                "symbol": symbol,
                "interval": interval,
                "start": start_time,
                "end": end_time,
                "limit": min(1000, hours - len(data_list))
            }
            
            try:
                response = requests.get(url, params=params, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data['retCode'] == 0:
                        chunk = data['result']['list']
                        if not chunk:
                            break
                        data_list.extend(chunk)
                        end_time = int(chunk[-1][0]) - 1
                    else:
                        print(f"    ❌ API 오류: {data['retMsg']}")
                        break
                else:
                    print(f"    ❌ HTTP 오류: {response.status_code}")
                    break
            except Exception as e:
                print(f"    ❌ 요청 오류: {e}")
                break
            
            time.sleep(0.05)  # 속도 최적화
        
        if not data_list:
            return pd.DataFrame()
        
        # 데이터프레임 생성
        df = pd.DataFrame(data_list, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'
        ])
        
        # 데이터 정리
        df = df[::-1].reset_index(drop=True)
        df['timestamp'] = pd.to_datetime(df['timestamp'].astype('int64'), unit='ms')
        numeric_cols = ['open', 'high', 'low', 'close', 'volume']
        df[numeric_cols] = df[numeric_cols].astype(float)
        
        print(f"    ✅ 수집 완료: {len(df)}개 캔들")
        return df
    
    def calculate_advanced_indicators(self, df):
        """고급 기술적 지표 계산 (main5 기반)"""
        # 기본 지표
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        
        # === SMC (Smart Money Concept) ===
        def calc_swing_high(series):
            if len(series) < 3:
                return np.nan
            return series.iloc[1] if (series.iloc[1] > series.iloc[0]) and (series.iloc[1] > series.iloc[2]) else np.nan

        def calc_swing_low(series):
            if len(series) < 3:
                return np.nan
            return series.iloc[1] if (series.iloc[1] < series.iloc[0]) and (series.iloc[1] < series.iloc[2]) else np.nan

        # Swing High/Low 계산
        df['swing_high'] = df['high'].rolling(window=3, min_periods=3).apply(calc_swing_high, raw=False)
        df['swing_low'] = df['low'].rolling(window=3, min_periods=3).apply(calc_swing_low, raw=False)
        
        # BOS (Break of Structure)
        df['bos_bullish'] = (df['high'] > df['swing_high'].shift(1)) & (df['swing_high'].shift(1).notna())
        df['bos_bearish'] = (df['low'] < df['swing_low'].shift(1)) & (df['swing_low'].shift(1).notna())
        
        # === 거래량 분석 ===
        # CVD (Cumulative Volume Delta)
        df['delta'] = np.where(df['close'] > df['open'], df['volume'], 
                              np.where(df['close'] < df['open'], -df['volume'], 0))
        df['cvd'] = df['delta'].cumsum()
        
        # OBV (On Balance Volume)
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()
        
        # === 전통적 지표 ===
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).fillna(0)
        loss = (-delta.where(delta < 0, 0)).fillna(0)
        avg_gain = gain.rolling(14, min_periods=1).mean()
        avg_loss = loss.rolling(14, min_periods=1).mean()
        rs = avg_gain / avg_loss.replace(0, np.nan).fillna(0.001)
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi'] = df['rsi'].clip(0, 100)
        
        # 이동평균선
        df['ma20'] = df['close'].rolling(20).mean()
        df['ma50'] = df['close'].rolling(50).mean()
        df['ma200'] = df['close'].rolling(200).mean()
        
        # 볼린저 밴드
        df['bb_middle'] = df['ma20']
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        
        # MACD
        ema12 = df['close'].ewm(span=12).mean()
        ema26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        return df
    
    def calculate_vpvr(self, df, bins=20):
        """VPVR (Volume Profile Visible Range) 계산"""
        price_range = df['typical_price'].max() - df['typical_price'].min()
        if price_range == 0:
            return pd.DataFrame()
        
        bin_size = price_range / bins
        price_bins = [df['typical_price'].min() + i * bin_size for i in range(bins + 1)]
        
        df_temp = df.copy()
        df_temp['price_bin'] = pd.cut(df_temp['typical_price'], bins=price_bins)
        
        vpvr = df_temp.groupby('price_bin', observed=False)['volume'].sum().reset_index()
        vpvr['poc'] = vpvr['price_bin'].apply(lambda x: x.mid if pd.notna(x) else np.nan)
        vpvr = vpvr.dropna()
        
        return vpvr.sort_values('volume', ascending=False)
    
    def analyze_period(self, df, period_hours):
        """기간별 분석"""
        period_data = df.tail(period_hours)
        
        if len(period_data) < 10:
            return None
        
        # 기본 분석
        high = period_data['high'].max()
        low = period_data['low'].min()
        current_price = df['close'].iloc[-1]
        high_ratio = (current_price - low) / (high - low) * 100 if high != low else 50
        
        # 거래량 집중구간 (VPVR)
        vpvr = self.calculate_vpvr(period_data, bins=10)
        volume_zones = []
        
        if not vpvr.empty:
            top_zones = vpvr.head(3)
            for _, row in top_zones.iterrows():
                volume_zones.append({
                    'price': row['poc'],
                    'volume': row['volume'],
                    'percent': (row['volume'] / vpvr['volume'].sum()) * 100
                })
        
        # SMC 신호
        recent_bos_bullish = period_data['bos_bullish'].tail(10).any()
        recent_bos_bearish = period_data['bos_bearish'].tail(10).any()
        
        # 기술적 지표
        rsi = df['rsi'].iloc[-1]
        macd = df['macd'].iloc[-1]
        macd_signal = df['macd_signal'].iloc[-1]
        
        return {
            'high': high,
            'low': low,
            'current_price': current_price,
            'high_ratio': high_ratio,
            'volume_zones': volume_zones,
            'bos_bullish': recent_bos_bullish,
            'bos_bearish': recent_bos_bearish,
            'rsi': rsi,
            'macd': macd,
            'macd_signal': macd_signal,
            'ma20': df['ma20'].iloc[-1],
            'ma50': df['ma50'].iloc[-1],
            'ma200': df['ma200'].iloc[-1]
        }
    
    def generate_trading_strategy(self, analysis):
        """고급 트레이딩 전략 생성"""
        strategies = []
        score = 0  # 신호 강도 점수
        
        # 1. SMC 기반 전략
        if analysis['bos_bullish']:
            strategies.append("🔥 BOS 상승 돌파 - 강한 매수 신호")
            score += 30
        elif analysis['bos_bearish']:
            strategies.append("❄️ BOS 하락 돌파 - 강한 매도 신호")
            score -= 30
        
        # 2. 고저비율 + RSI 복합 전략
        high_ratio = analysis['high_ratio']
        rsi = analysis['rsi']
        
        if high_ratio < 20 and rsi < 30:
            strategies.append("💎 저가 + 과매도 - 강한 매수 기회")
            score += 25
        elif high_ratio > 80 and rsi > 70:
            strategies.append("⚠️ 고가 + 과매수 - 강한 매도 신호")
            score -= 25
        elif high_ratio < 30 and rsi < 50:
            strategies.append("📈 저가권 진입 - 매수 고려")
            score += 15
        elif high_ratio > 70 and rsi > 60:
            strategies.append("📉 고가권 진입 - 매도 고려")
            score -= 15
        
        # 3. MACD 신호
        if analysis['macd'] > analysis['macd_signal']:
            strategies.append("📊 MACD 골든크로스 - 상승 모멘텀")
            score += 10
        else:
            strategies.append("📊 MACD 데드크로스 - 하락 모멘텀")
            score -= 10
        
        # 4. 거래량 집중구간 전략
        if analysis['volume_zones']:
            main_zone = analysis['volume_zones'][0]
            current_price = analysis['current_price']
            
            if current_price > main_zone['price']:
                strategies.append(f"🎯 주요 거래량 구간({main_zone['price']:.2f}) 위 - 지지 역할 기대")
                score += 5
            else:
                strategies.append(f"🎯 주요 거래량 구간({main_zone['price']:.2f}) 아래 - 저항 역할 예상")
                score -= 5
        
        # 5. 이동평균 정렬
        ma20, ma50, ma200 = analysis['ma20'], analysis['ma50'], analysis['ma200']
        current_price = analysis['current_price']
        
        if current_price > ma20 > ma50 > ma200:
            strategies.append("🚀 완벽한 상승 정렬 - 강한 상승 추세")
            score += 20
        elif current_price < ma20 < ma50 < ma200:
            strategies.append("💥 완벽한 하락 정렬 - 강한 하락 추세")
            score -= 20
        
        # 최종 신호 결정
        if score >= 40:
            final_signal = "🔥 STRONG BUY"
        elif score >= 20:
            final_signal = "📈 BUY"
        elif score <= -40:
            final_signal = "💥 STRONG SELL"
        elif score <= -20:
            final_signal = "📉 SELL"
        else:
            final_signal = "⏸️ HOLD"
        
        return {
            'strategies': strategies,
            'score': score,
            'final_signal': final_signal
        }
    
    def print_analysis_results(self, symbol, period_name, analysis, strategy):
        """분석 결과 출력"""
        print(f"\n[{period_name} 분석]")
        print(f"  💰 현재가: {analysis['current_price']:.2f}")
        print(f"  📊 고저비율: {analysis['high_ratio']:.1f}%")
        print(f"  📈 RSI: {analysis['rsi']:.1f}")
        
        # 거래량 집중구간
        if analysis['volume_zones']:
            print(f"\n  🎯 거래량 집중구간:")
            for i, zone in enumerate(analysis['volume_zones'][:2]):
                position = "위" if analysis['current_price'] > zone['price'] else "아래"
                print(f"    {i+1}. {zone['price']:.2f} (현재가 {position}) - {zone['percent']:.1f}%")
        
        # 전략
        print(f"\n  🚀 트레이딩 전략:")
        for strategy_text in strategy['strategies'][:3]:  # 상위 3개만 출력
            print(f"    • {strategy_text}")
        
        print(f"\n  🎯 최종 신호: {strategy['final_signal']} (점수: {strategy['score']})")
        print(f"  {'='*50}")
    
    def analyze_symbol(self, symbol):
        """심볼 종합 분석"""
        print(f"\n🔍 {symbol} 분석 시작")
        print(f"{'='*60}")
        
        # 데이터 수집
        df = self.fetch_bybit_data(symbol, hours=2400)  # 100일치
        if df.empty:
            print(f"❌ {symbol} 데이터 수집 실패")
            return None
        
        # 기술적 지표 계산
        print(f"  🔧 기술적 지표 계산 중...")
        df = self.calculate_advanced_indicators(df)
        
        # 기간별 분석
        results = {}
        for period_name, period_hours in self.periods.items():
            if len(df) >= period_hours:
                analysis = self.analyze_period(df, period_hours)
                if analysis:
                    strategy = self.generate_trading_strategy(analysis)
                    results[period_name] = {
                        'analysis': analysis,
                        'strategy': strategy
                    }
                    self.print_analysis_results(symbol, period_name, analysis, strategy)
        
        # 시각화
        self.create_advanced_chart(df, symbol, results)
        
        return results
    
    def create_advanced_chart(self, df, symbol, results):
        """고급 차트 생성 (main6 스타일 + main5 분석)"""
        fig, axes = plt.subplots(4, 1, figsize=(16, 20))

        # 최근 500개 데이터만 시각화 (속도 최적화)
        plot_df = df.tail(500).copy()

        # 1. 가격 차트 + SMC + 볼린저 밴드
        ax1 = axes[0]
        ax1.plot(plot_df['timestamp'], plot_df['close'], label='Price', color='black', linewidth=1.5)
        ax1.plot(plot_df['timestamp'], plot_df['ma20'], label='MA20', color='blue', alpha=0.7)
        ax1.plot(plot_df['timestamp'], plot_df['ma50'], label='MA50', color='orange', alpha=0.7)
        ax1.plot(plot_df['timestamp'], plot_df['ma200'], label='MA200', color='red', alpha=0.7)

        # 볼린저 밴드
        ax1.fill_between(plot_df['timestamp'], plot_df['bb_upper'], plot_df['bb_lower'],
                        color='gray', alpha=0.2, label='Bollinger Bands')

        # SMC - Swing High/Low 표시
        swing_highs = plot_df[plot_df['swing_high'].notna()]
        swing_lows = plot_df[plot_df['swing_low'].notna()]

        if not swing_highs.empty:
            ax1.scatter(swing_highs['timestamp'], swing_highs['swing_high'],
                       color='red', marker='v', s=50, alpha=0.7, label='Swing High')
        if not swing_lows.empty:
            ax1.scatter(swing_lows['timestamp'], swing_lows['swing_low'],
                       color='green', marker='^', s=50, alpha=0.7, label='Swing Low')

        # BOS 신호 표시
        bos_bull = plot_df[plot_df['bos_bullish']]
        bos_bear = plot_df[plot_df['bos_bearish']]

        if not bos_bull.empty:
            ax1.scatter(bos_bull['timestamp'], bos_bull['high'],
                       color='lime', marker='▲', s=100, alpha=0.8, label='BOS Bullish')
        if not bos_bear.empty:
            ax1.scatter(bos_bear['timestamp'], bos_bear['low'],
                       color='red', marker='▼', s=100, alpha=0.8, label='BOS Bearish')

        ax1.set_title(f'{symbol} - 가격 + SMC + 볼린저 밴드', fontsize=14, fontweight='bold')
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 2. RSI + MACD
        ax2 = axes[1]
        ax2_twin = ax2.twinx()

        # RSI
        ax2.plot(plot_df['timestamp'], plot_df['rsi'], label='RSI', color='purple', linewidth=1.5)
        ax2.axhline(y=70, color='r', linestyle='--', alpha=0.5)
        ax2.axhline(y=30, color='g', linestyle='--', alpha=0.5)
        ax2.fill_between(plot_df['timestamp'], 70, 100, color='red', alpha=0.1)
        ax2.fill_between(plot_df['timestamp'], 0, 30, color='green', alpha=0.1)
        ax2.set_ylabel('RSI', color='purple')
        ax2.set_ylim(0, 100)

        # MACD
        ax2_twin.plot(plot_df['timestamp'], plot_df['macd'], label='MACD', color='blue', linewidth=1)
        ax2_twin.plot(plot_df['timestamp'], plot_df['macd_signal'], label='Signal', color='red', linewidth=1)
        ax2_twin.bar(plot_df['timestamp'], plot_df['macd_histogram'],
                    label='Histogram', color='gray', alpha=0.6, width=0.8)
        ax2_twin.set_ylabel('MACD', color='blue')
        ax2_twin.axhline(y=0, color='black', linestyle='-', alpha=0.3)

        ax2.set_title('RSI + MACD', fontsize=12, fontweight='bold')
        ax2.legend(loc='upper left')
        ax2_twin.legend(loc='upper right')
        ax2.grid(True, alpha=0.3)

        # 3. CVD (Cumulative Volume Delta)
        ax3 = axes[2]
        ax3.plot(plot_df['timestamp'], plot_df['cvd'], label='CVD', color='green', linewidth=1.5)
        ax3.plot(plot_df['timestamp'], plot_df['cvd'].rolling(20).mean(),
                label='CVD MA(20)', color='darkgreen', linestyle='--', alpha=0.8)
        ax3.set_title('Cumulative Volume Delta (CVD)', fontsize=12, fontweight='bold')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. OBV + Volume
        ax4 = axes[3]
        ax4_twin = ax4.twinx()

        # OBV
        ax4.plot(plot_df['timestamp'], plot_df['obv'], label='OBV', color='blue', linewidth=1.5)
        ax4.set_ylabel('OBV', color='blue')

        # Volume
        ax4_twin.bar(plot_df['timestamp'], plot_df['volume'],
                    label='Volume', color='gray', alpha=0.6, width=0.8)
        ax4_twin.set_ylabel('Volume', color='gray')

        ax4.set_title('OBV + Volume', fontsize=12, fontweight='bold')
        ax4.legend(loc='upper left')
        ax4_twin.legend(loc='upper right')
        ax4.grid(True, alpha=0.3)

        # X축 날짜 포맷팅
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=24))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 파일 저장
        filename = f"{symbol}_advanced_analysis.png"
        save_path = os.path.join(self.save_dir, filename)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"  📊 차트 저장: {save_path}")

        return save_path

    def run_analysis(self):
        """전체 분석 실행"""
        print("🚀 고급 암호화폐 분석 시스템 시작")
        print("📊 SMC + VPVR + 거래량 분석 + 시각화")
        print("="*70)

        all_results = {}

        for symbol in self.symbols:
            try:
                results = self.analyze_symbol(symbol)
                if results:
                    all_results[symbol] = results
                time.sleep(1)  # API 제한 고려
            except Exception as e:
                print(f"❌ {symbol} 분석 오류: {e}")

        print(f"\n✅ 분석 완료! 결과는 {self.save_dir} 폴더에 저장되었습니다.")
        return all_results


def main():
    """메인 실행 함수"""
    analyzer = AdvancedCryptoAnalyzer()
    results = analyzer.run_analysis()
    
    # 요약 출력
    print(f"\n📋 분석 요약:")
    for symbol, symbol_results in results.items():
        if '20일' in symbol_results:
            strategy = symbol_results['20일']['strategy']
            print(f"  {symbol}: {strategy['final_signal']} (점수: {strategy['score']})")


if __name__ == "__main__":
    main()
