import os
import time
import pyupbit
from dotenv import load_dotenv
from datetime import datetime

# 환경변수 로드
load_dotenv()

class UpbitTradingBot:
    def __init__(self):
        self.upbit = pyupbit.Upbit(os.getenv("UPBIT_ACCESS_KEY"), os.getenv("UPBIT_SECRET_KEY"))
        self.coins = ["KRW-BTC", "KRW-ETH", "KRW-SOL"]
        self.timeframe = "minute5"
        self.max_per_coin = 100000  # 종목당 최대 10만원
        self.order_amount = 15000    # 1회 매수 금액 (1.5만원)

    def get_indicators(self, df):
        """RSI 6과 볼린저밴드 계산"""
        # RSI 6
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(6).mean()
        avg_loss = loss.rolling(6).mean()
        rs = avg_gain / avg_loss
        df['rsi6'] = 100 - (100 / (1 + rs))
        
        # 볼린저밴드 (20기간, 2σ)
        df['bb_mid'] = df['close'].rolling(20).mean()
        df['bb_upper'] = df['bb_mid'] + 2 * df['close'].rolling(20).std()
        df['bb_lower'] = df['bb_mid'] - 2 * df['close'].rolling(20).std()
        
        return df

    def check_signal(self, coin):
        """매매 신호 확인"""
        df = pyupbit.get_ohlcv(coin, interval=self.timeframe, count=50)
        if df is None or len(df) < 20:
            return False, False
            
        df = self.get_indicators(df)
        last = df.iloc[-1]
        prev = df.iloc[-2]
        
        buy_signal = (prev['rsi6'] < 30) and (last['rsi6'] > 30) and (last['close'] <= last['bb_lower'])
        sell_signal = (prev['rsi6'] > 65) and (last['rsi6'] < 65)
        
        return buy_signal, sell_signal

    def execute_trade(self):
        """매매 실행 로직"""
        for coin in self.coins:
            try:
                buy_signal, sell_signal = self.check_signal(coin)
                coin_balance = self.upbit.get_balance(coin.split("-")[1])
                krw_balance = self.upbit.get_balance("KRW")
                current_price = pyupbit.get_current_price(coin)
                
                # 매도 조건
                if sell_signal and coin_balance > 0:
                    self.upbit.sell_market_order(coin, coin_balance)
                    print(f"[{datetime.now()}] {coin} 전량 매도 완료")
                
                # 매수 조건
                elif buy_signal and krw_balance >= self.order_amount:
                    # 종목당 누적 투자금액 계산
                    invested = self.max_per_coin - (krw_balance - self.upbit.get_balance("KRW"))
                    available = min(self.order_amount, self.max_per_coin - invested)
                    
                    if available >= 5000:  # 최소 주문 금액
                        self.upbit.buy_market_order(coin, available)
                        print(f"[{datetime.now()}] {coin} 매수 완료 ({available:,.0f}원)")
                        
            except Exception as e:
                print(f"{coin} 처리 중 오류: {e}")

    def run(self):
        """실행 메인 루프"""
        print("===== 자동매매 시작 =====")
        print(f"설정: 종목당 {self.max_per_coin:,}원 한도, {self.order_amount:,}원 단위 매수")
        while True:
            try:
                self.execute_trade()
                time.sleep(60)  # 1분 간격 실행
            except Exception as e:
                print(f"시스템 오류: {e}")
                time.sleep(300)

if __name__ == "__main__":
    bot = UpbitTradingBot()
    bot.run()