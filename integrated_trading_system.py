"""
통합 트레이딩 시스템 - 업비트 데이터 분석 + 업비트 매매
"""
import pandas as pd
import numpy as np
import pyupbit
import time
import os
from datetime import datetime
from dotenv import load_dotenv
import warnings
warnings.filterwarnings('ignore')

# 기존 전략 시스템 임포트
from trading_system import TradingSystem, StrategyConfigs
from trading_system.core.base_strategy import SignalType

# 환경변수 로드
load_dotenv()


class IntegratedUpbitSystem:
    """업비트 통합 트레이딩 시스템"""
    
    def __init__(self, live_trading: bool = False):
        """
        Args:
            live_trading: 실제 거래 여부 (False=시뮬레이션)
        """
        self.live_trading = live_trading
        
        # 업비트 API 초기화
        if live_trading:
            access_key = os.getenv("UPBIT_ACCESS_KEY")
            secret_key = os.getenv("UPBIT_SECRET_KEY")
            if not access_key or not secret_key:
                raise ValueError("업비트 API 키가 설정되지 않았습니다!")
            self.upbit = pyupbit.Upbit(access_key, secret_key)
        else:
            self.upbit = None
            print("⚠️  시뮬레이션 모드 - 실제 거래하지 않습니다")
        
        # 트레이딩 시스템 초기화
        self.trading_system = TradingSystem("default")
        
        # 거래 설정
        self.symbols = ["KRW-BTC", "KRW-ETH", "KRW-SOL", "KRW-XRP"]
        self.timeframe = "minute5"
        self.max_investment_per_coin = 100000  # 종목당 최대 10만원
        self.order_amount = 15000  # 1회 주문 금액
        self.min_order_amount = 5000  # 최소 주문 금액
        
        # 상태 추적
        self.last_signals = {}
        self.positions = {}
        
        print(f"🚀 통합 업비트 시스템 초기화 완료")
        print(f"   - 실제 거래: {'ON' if live_trading else 'OFF'}")
        print(f"   - 대상 종목: {len(self.symbols)}개")
        print(f"   - 전략 수: {self.trading_system.get_system_status()['total_strategies']}개")
    
    def get_upbit_data(self, symbol: str, count: int = 200) -> pd.DataFrame:
        """업비트에서 데이터 수집"""
        try:
            df = pyupbit.get_ohlcv(symbol, interval=self.timeframe, count=count)
            if df is None or df.empty:
                print(f"❌ {symbol} 데이터 수집 실패")
                return pd.DataFrame()
            
            # 컬럼명 표준화
            df.columns = ['open', 'high', 'low', 'close', 'volume']
            return df
            
        except Exception as e:
            print(f"❌ {symbol} 데이터 수집 오류: {e}")
            return pd.DataFrame()
    
    def analyze_symbol(self, symbol: str) -> dict:
        """심볼 분석"""
        # 데이터 수집
        df = self.get_upbit_data(symbol)
        if df.empty:
            return {'error': 'data_collection_failed'}
        
        try:
            # 전략 시스템으로 분석
            analysis = self.trading_system.analyze_symbol(df, symbol)
            
            # 현재 가격 정보 추가
            current_price = pyupbit.get_current_price(symbol)
            analysis['current_market_price'] = current_price
            
            return analysis
            
        except Exception as e:
            print(f"❌ {symbol} 분석 오류: {e}")
            return {'error': str(e)}
    
    def get_account_info(self) -> dict:
        """계좌 정보 조회"""
        if not self.live_trading or not self.upbit:
            return {'krw_balance': 1000000, 'positions': {}}  # 시뮬레이션
        
        try:
            # KRW 잔고
            krw_balance = self.upbit.get_balance("KRW")
            
            # 보유 코인 잔고
            positions = {}
            for symbol in self.symbols:
                coin_name = symbol.split("-")[1]
                balance = self.upbit.get_balance(coin_name)
                if balance > 0:
                    current_price = pyupbit.get_current_price(symbol)
                    positions[symbol] = {
                        'balance': balance,
                        'current_price': current_price,
                        'value': balance * current_price
                    }
            
            return {
                'krw_balance': krw_balance,
                'positions': positions,
                'total_value': krw_balance + sum(pos['value'] for pos in positions.values())
            }
            
        except Exception as e:
            print(f"❌ 계좌 정보 조회 오류: {e}")
            return {'error': str(e)}
    
    def execute_trade(self, symbol: str, signal: str, confidence: float) -> bool:
        """거래 실행"""
        if not self.live_trading:
            print(f"📝 [시뮬레이션] {symbol}: {signal} (신뢰도: {confidence:.2f})")
            return True
        
        try:
            account = self.get_account_info()
            if 'error' in account:
                return False
            
            coin_name = symbol.split("-")[1]
            current_price = pyupbit.get_current_price(symbol)
            
            if signal in ['Buy', 'StrongBuy']:
                # 매수 로직
                available_krw = account['krw_balance']
                
                # 종목당 투자 한도 확인
                current_position_value = account['positions'].get(symbol, {}).get('value', 0)
                remaining_limit = self.max_investment_per_coin - current_position_value
                
                # 실제 주문 금액 계산
                order_amount = min(self.order_amount, available_krw, remaining_limit)
                
                if order_amount >= self.min_order_amount:
                    result = self.upbit.buy_market_order(symbol, order_amount)
                    if result:
                        print(f"✅ 매수 완료: {symbol} {order_amount:,}원")
                        return True
                    else:
                        print(f"❌ 매수 실패: {symbol}")
                        return False
                else:
                    print(f"⚠️  매수 스킵: {symbol} (주문금액 부족: {order_amount:,}원)")
                    return False
            
            elif signal in ['Sell', 'StrongSell']:
                # 매도 로직
                coin_balance = self.upbit.get_balance(coin_name)
                
                if coin_balance > 0:
                    result = self.upbit.sell_market_order(symbol, coin_balance)
                    if result:
                        print(f"✅ 매도 완료: {symbol} {coin_balance:.8f}개")
                        return True
                    else:
                        print(f"❌ 매도 실패: {symbol}")
                        return False
                else:
                    print(f"⚠️  매도 스킵: {symbol} (보유량 없음)")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ {symbol} 거래 실행 오류: {e}")
            return False
    
    def run_analysis_cycle(self):
        """분석 사이클 실행"""
        print(f"\n{'='*60}")
        print(f"🔍 분석 시작: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        # 계좌 정보 출력
        account = self.get_account_info()
        if 'error' not in account:
            print(f"💰 KRW 잔고: {account['krw_balance']:,.0f}원")
            if account['positions']:
                print("📊 보유 포지션:")
                for symbol, pos in account['positions'].items():
                    print(f"   {symbol}: {pos['balance']:.6f}개 ({pos['value']:,.0f}원)")
        
        # 각 심볼 분석
        analysis_results = {}
        for symbol in self.symbols:
            print(f"\n📈 {symbol} 분석 중...")
            
            analysis = self.analyze_symbol(symbol)
            if 'error' in analysis:
                print(f"❌ {symbol} 분석 실패: {analysis['error']}")
                continue
            
            analysis_results[symbol] = analysis
            
            # 결과 출력
            composite = analysis['composite_strategy']
            summary = analysis['summary']
            
            print(f"   현재가: {analysis.get('current_market_price', 0):,.0f}원")
            print(f"   복합신호: {composite['signal']} (신뢰도: {composite['confidence']:.2f})")
            print(f"   개별신호: 매수 {summary['buy_signals']}개, "
                  f"매도 {summary['sell_signals']}개, 보류 {summary['hold_signals']}개")
            
            # 거래 실행
            signal = composite['signal']
            confidence = composite['confidence']
            
            # 신호 변화 확인 (같은 신호 반복 방지)
            last_signal = self.last_signals.get(symbol)
            if last_signal != signal:
                if signal in ['Buy', 'StrongBuy', 'Sell', 'StrongSell']:
                    success = self.execute_trade(symbol, signal, confidence)
                    if success:
                        self.last_signals[symbol] = signal
                else:
                    print(f"⏸️  {symbol}: 보류 ({signal})")
            else:
                print(f"🔄 {symbol}: 신호 변화 없음 ({signal})")
        
        return analysis_results
    
    def run_continuous(self, interval_minutes: int = 5):
        """연속 실행"""
        print(f"🔄 연속 실행 시작 (간격: {interval_minutes}분)")
        print("Ctrl+C로 중지할 수 있습니다.")
        
        try:
            while True:
                self.run_analysis_cycle()
                
                print(f"\n⏰ {interval_minutes}분 대기 중...")
                time.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            print("\n🛑 사용자에 의해 중지되었습니다.")
        except Exception as e:
            print(f"\n❌ 시스템 오류: {e}")
    
    def run_single_analysis(self):
        """단일 분석 실행"""
        return self.run_analysis_cycle()


def main():
    """메인 실행 함수"""
    print("🚀 업비트 통합 트레이딩 시스템")
    print("=" * 50)
    
    # 실행 모드 선택
    print("실행 모드를 선택하세요:")
    print("1. 시뮬레이션 모드 (안전)")
    print("2. 실제 거래 모드 (주의!)")
    
    try:
        choice = input("선택 (1 또는 2): ").strip()
        
        if choice == "1":
            live_trading = False
            print("✅ 시뮬레이션 모드 선택")
        elif choice == "2":
            live_trading = True
            confirm = input("⚠️  실제 거래 모드입니다. 정말 진행하시겠습니까? (yes/no): ").strip().lower()
            if confirm != "yes":
                print("❌ 취소되었습니다.")
                return
            print("⚠️  실제 거래 모드 선택")
        else:
            print("❌ 잘못된 선택입니다.")
            return
        
        # 시스템 초기화
        system = IntegratedUpbitSystem(live_trading=live_trading)
        
        # 실행 방식 선택
        print("\n실행 방식을 선택하세요:")
        print("1. 단일 분석")
        print("2. 연속 실행 (5분 간격)")
        
        run_choice = input("선택 (1 또는 2): ").strip()
        
        if run_choice == "1":
            system.run_single_analysis()
        elif run_choice == "2":
            system.run_continuous(5)
        else:
            print("❌ 잘못된 선택입니다.")
            
    except Exception as e:
        print(f"❌ 시스템 오류: {e}")


if __name__ == "__main__":
    main()
