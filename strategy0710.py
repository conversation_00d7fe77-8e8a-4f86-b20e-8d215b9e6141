import pandas as pd
import ta
import pyupbit
import numpy as np
from ta.momentum import RSIIndicator
from ta.trend import MACD
import warnings
warnings.filterwarnings('ignore')

def basic_analyze(df):
    """기본 기술적 지표 계산"""
    try:
        df = df.copy()
        df['PVT'] = (df['close'].pct_change() * df['volume']).cumsum()
        df['PVT_MA'] = df['PVT'].rolling(20).mean()
        df['RSI'] = RSIIndicator(df['close'], 14).rsi()
        macd = MACD(df['close'], 12, 26, 9)
        df['MACD'] = macd.macd()
        df['MACD_signal'] = macd.macd_signal()
        return df
    except Exception as e:
        print(f"Error in basic_analyze: {e}")
        return df

def ehlers_dft_adapted_rsi(df, window=50, frac=0.5):
    """Ehlers DFT 적응형 RSI 계산"""
    try:
        if len(df) < window:
            return pd.Series([50] * len(df), index=df.index)
            
        price = (df['high'] + df['low']) / 2
        cleaned_data = price.copy()

        # Detrending
        hp = price.copy()
        alpha1 = (1 - np.sin(2 * np.pi / 40)) / np.cos(2 * np.pi / 40)
        for i in range(6, len(price)):
            hp.iloc[i] = 0.5 * (1 + alpha1) * (price.iloc[i] - price.iloc[i-1]) + alpha1 * hp.iloc[i-1]
            cleaned_data.iloc[i] = (hp.iloc[i] + 2*hp.iloc[i-1] + 3*hp.iloc[i-2] +
                                    3*hp.iloc[i-3] + 2*hp.iloc[i-4] + hp.iloc[i-5]) / 12

        # DFT Power Spectrum
        powers = []
        for period in range(8, 51):
            cosine_sum, sine_sum = 0.0, 0.0
            for n in range(min(window, len(cleaned_data))):
                angle = 2 * np.pi * n / period
                cosine_sum += cleaned_data.iloc[-n-1] * np.cos(angle)
                sine_sum += cleaned_data.iloc[-n-1] * np.sin(angle)
            power = cosine_sum**2 + sine_sum**2
            powers.append(power)

        max_power = max(powers) if powers else 1
        dbs = []
        for p in powers:
            if p > 0 and max_power > 0:
                db = -10 * np.log10(0.01 / (1 - 0.99 * p / max_power))
                dbs.append(min(db, 20))
            else:
                dbs.append(0)

        # Dominant cycle
        numerator = 0.0
        denominator = 0.0
        for i, db in enumerate(dbs):
            if db < 3:
                weight = 3 - db
                period = i + 8
                numerator += period * weight
                denominator += weight
        dominant_cycle = numerator / denominator if denominator != 0 else 20

        # RSI Calculation
        dynamic_len = int(min(window, np.ceil(frac * dominant_cycle)))
        delta = price.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(window=dynamic_len).mean()
        avg_loss = loss.rolling(window=dynamic_len).mean()
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return rsi.fillna(50)
    except Exception as e:
        print(f"Error in ehlers_dft_adapted_rsi: {e}")
        return pd.Series([50] * len(df), index=df.index)

def pvt_cross_strategy(df):
    """PVT 크로스 전략"""
    try:
        df = basic_analyze(df)
        if len(df) < 3:
            return "Hold"
        if df['PVT'].iloc[-1] > df['PVT_MA'].iloc[-1] and df['PVT'].iloc[-2] <= df['PVT_MA'].iloc[-2]:
            return "Buy"
        elif df['PVT'].iloc[-1] < df['PVT_MA'].iloc[-1] and df['PVT'].iloc[-2] >= df['PVT_MA'].iloc[-2]:
            return "Sell"
        else:
            return "Hold"
    except Exception as e:
        print(f"Error in pvt_cross_strategy: {e}")
        return "Hold"

def pvt_trend_10min(df_10m):
    """10분봉 PVT 트렌드 분석"""
    try:
        if len(df_10m) < 2:
            return "Hold"
        df_10m = df_10m.copy()
        df_10m['PVT'] = (df_10m['close'].pct_change() * df_10m['volume']).cumsum()
        if df_10m['PVT'].iloc[-1] > df_10m['PVT'].iloc[0]:
            return "Buy"
        elif df_10m['PVT'].iloc[-1] < df_10m['PVT'].iloc[0]:
            return "Sell"
        else:
            return "Hold"
    except Exception as e:
        print(f"Error in pvt_trend_10min: {e}")
        return "Hold"

def ichimoku_custom(df, c=9, b=26, l=52):
    """커스텀 이치모쿠 계산"""
    try:
        conv = (df['high'].rolling(c).max() + df['low'].rolling(c).min()) / 2
        base = (df['high'].rolling(b).max() + df['low'].rolling(b).min()) / 2
        span_a = ((conv + base) / 2).shift(b)
        span_b = ((df['high'].rolling(l).max() + df['low'].rolling(l).min()) / 2).shift(b)
        return span_a, span_b
    except Exception as e:
        print(f"Error in ichimoku_custom: {e}")
        return pd.Series([0] * len(df), index=df.index), pd.Series([0] * len(df), index=df.index)

def safe_get_30m_data(symbol):
    """30분봉 데이터 안전하게 가져오기"""
    try:
        if symbol:
            df_30m = pyupbit.get_ohlcv(symbol, interval="minute30", count=100)
            if df_30m is not None and len(df_30m) > 62:
                df_30m['rsi55'] = ta.momentum.RSIIndicator(df_30m['close'], window=55).rsi()
                df_30m['rsi_signal62'] = df_30m['rsi55'].rolling(62).mean()
                return df_30m['rsi55'].iloc[-1], df_30m['rsi_signal62'].iloc[-1]
    except Exception as e:
        print(f"Error getting 30m data: {e}")
    return 50, 50  # 기본값 반환

def resample_to_10min(df):
    """1분봉을 10분봉으로 리샘플링"""
    try:
        df_copy = df.copy()
        df_copy.index = pd.to_datetime(df_copy.index)
        df_10m = df_copy.resample('10min').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
        return df_10m
    except Exception as e:
        print(f"Error in resample_to_10min: {e}")
        return df.copy()

def analyze_all_strategies(df, symbol=None):
    """모든 전략 분석 - 통합된 단일 함수"""
    results = {}
    
    try:
        # 데이터 유효성 검사
        if df is None or len(df) < 50:
            print("Insufficient data for analysis")
            return {"Error": "Insufficient data"}
        
        df = df.copy()
        
        # ===== Strategy 1: RSI =====
        try:
            df['rsi'] = ta.momentum.RSIIndicator(df['close'], window=14).rsi()
            rsi = df['rsi'].iloc[-1]
            if pd.notna(rsi):
                if rsi < 30:
                    results['RSI'] = 'Buy'
                elif rsi > 70:
                    results['RSI'] = 'Sell'
                else:
                    results['RSI'] = 'Hold'
            else:
                results['RSI'] = 'Hold'
        except Exception as e:
            results['RSI'] = 'Error'
            print(f"RSI Strategy Error: {e}")

        # ===== Strategy 2: MACD =====
        try:
            macd = ta.trend.MACD(df['close'])
            df['macd'] = macd.macd()
            df['macd_signal'] = macd.macd_signal()
            latest_macd = df['macd'].iloc[-1]
            latest_signal = df['macd_signal'].iloc[-1]
            if pd.notna(latest_macd) and pd.notna(latest_signal):
                if latest_macd > latest_signal:
                    results['MACD'] = 'Buy'
                elif latest_macd < latest_signal:
                    results['MACD'] = 'Sell'
                else:
                    results['MACD'] = 'Hold'
            else:
                results['MACD'] = 'Hold'
        except Exception as e:
            results['MACD'] = 'Error'
            print(f"MACD Strategy Error: {e}")

        # ===== Strategy 3: Bollinger Band Breakout =====
        try:
            bb = ta.volatility.BollingerBands(df['close'], window=20, window_dev=2)
            upper = bb.bollinger_hband().iloc[-1]
            lower = bb.bollinger_lband().iloc[-1]
            close = df['close'].iloc[-1]
            if pd.notna(upper) and pd.notna(lower) and pd.notna(close):
                if close > upper:
                    results['Bollinger'] = 'Sell'
                elif close < lower:
                    results['Bollinger'] = 'Buy'
                else:
                    results['Bollinger'] = 'Hold'
            else:
                results['Bollinger'] = 'Hold'
        except Exception as e:
            results['Bollinger'] = 'Error'
            print(f"Bollinger Strategy Error: {e}")

        # ===== Strategy 4: Double Bottom =====
        try:
            if len(df) >= 10:
                recent_lows = df['low'].tail(10).values
                double_bottom = len(recent_lows) >= 3 and recent_lows[-1] > recent_lows[-2] < recent_lows[-3]
                results['DoubleBottom'] = 'Buy' if double_bottom else 'Hold'
            else:
                results['DoubleBottom'] = 'Hold'
        except Exception as e:
            results['DoubleBottom'] = 'Error'
            print(f"DoubleBottom Strategy Error: {e}")

        # ===== Strategy 5: WaveTrend (10분봉 기준) =====
        try:
            df_10m = resample_to_10min(df)
            if len(df_10m) >= 21:
                hlc3 = (df_10m['high'] + df_10m['low'] + df_10m['close']) / 3
                esa = ta.trend.EMAIndicator(hlc3, window=10).ema_indicator()
                d = ta.trend.EMAIndicator((hlc3 - esa).abs(), window=10).ema_indicator()
                ci = (hlc3 - esa) / (0.015 * d)
                tci = ta.trend.EMAIndicator(ci, window=21).ema_indicator()
                wt1 = tci
                wt2 = wt1.rolling(4).mean()

                if pd.notna(wt1.iloc[-1]) and pd.notna(wt2.iloc[-1]):
                    if wt1.iloc[-1] > wt2.iloc[-1]:
                        results['WaveTrend'] = 'Buy'
                    elif wt1.iloc[-1] < wt2.iloc[-1]:
                        results['WaveTrend'] = 'Sell'
                    else:
                        results['WaveTrend'] = 'Hold'
                else:
                    results['WaveTrend'] = 'Hold'
            else:
                results['WaveTrend'] = 'Hold'
        except Exception as e:
            results['WaveTrend'] = 'Error'
            print(f"WaveTrend Strategy Error: {e}")

        # ===== Strategy 6: RSI Histogram Crossover =====
        try:
            if 'rsi' in df.columns:
                df['rsi_hist'] = df['rsi'] - df['rsi'].rolling(9).mean()
                hist = df['rsi_hist']
                if len(hist) >= 2 and pd.notna(hist.iloc[-1]) and pd.notna(hist.iloc[-2]):
                    if hist.iloc[-1] > 0 and hist.iloc[-2] <= 0:
                        results['RSI_Histogram'] = 'Buy'
                    elif hist.iloc[-1] < 0 and hist.iloc[-2] >= 0:
                        results['RSI_Histogram'] = 'Sell'
                    else:
                        results['RSI_Histogram'] = 'Hold'
                else:
                    results['RSI_Histogram'] = 'Hold'
            else:
                results['RSI_Histogram'] = 'Hold'
        except Exception as e:
            results['RSI_Histogram'] = 'Error'
            print(f"RSI_Histogram Strategy Error: {e}")

        # ===== Strategy 7: Ichimoku Cloud =====
        try:
            if len(df) >= 52:
                ichimoku = ta.trend.IchimokuIndicator(df['high'], df['low'], window1=9, window2=26, window3=52)
                leading_span_a = ichimoku.ichimoku_a().iloc[-1]
                leading_span_b = ichimoku.ichimoku_b().iloc[-1]
                price = df['close'].iloc[-1]
                if pd.notna(leading_span_a) and pd.notna(leading_span_b) and pd.notna(price):
                    if price > max(leading_span_a, leading_span_b):
                        results['Ichimoku'] = 'Buy'
                    elif price < min(leading_span_a, leading_span_b):
                        results['Ichimoku'] = 'Sell'
                    else:
                        results['Ichimoku'] = 'Hold'
                else:
                    results['Ichimoku'] = 'Hold'
            else:
                results['Ichimoku'] = 'Hold'
        except Exception as e:
            results['Ichimoku'] = 'Error'
            print(f"Ichimoku Strategy Error: {e}")

        # ===== Strategy 8: RSI(55) vs Signal(62) 1분봉 + 30분봉 강화 =====
        try:
            if len(df) >= 62:
                df['rsi55'] = ta.momentum.RSIIndicator(df['close'], window=55).rsi()
                df['rsi_signal62'] = df['rsi55'].rolling(62).mean()

                rsi_1m = df['rsi55'].iloc[-1]
                signal_1m = df['rsi_signal62'].iloc[-1]

                rsi_30m, signal_30m = safe_get_30m_data(symbol)

                if pd.notna(rsi_1m) and pd.notna(signal_1m):
                    if rsi_1m > signal_1m and rsi_1m >= 50:
                        if rsi_30m > signal_30m and rsi_30m >= 50:
                            results['RSI_Combo_Strong'] = 'StrongBuy'
                        else:
                            results['RSI_Combo_Strong'] = 'Buy'
                    elif rsi_1m < signal_1m and rsi_1m <= 49:
                        results['RSI_Combo_Strong'] = 'Sell'
                    else:
                        results['RSI_Combo_Strong'] = 'Hold'
                else:
                    results['RSI_Combo_Strong'] = 'Hold'
            else:
                results['RSI_Combo_Strong'] = 'Hold'
        except Exception as e:
            results['RSI_Combo_Strong'] = 'Error'
            print(f"RSI_Combo_Strong Strategy Error: {e}")

        # ===== Strategy 9: Z-Score of Close Price =====
        try:
            window_z = 20
            if len(df) >= window_z:
                mean_close = df['close'].rolling(window_z).mean()
                std_close = df['close'].rolling(window_z).std()
                zscore = (df['close'] - mean_close) / std_close
                z = zscore.iloc[-1]
                if pd.notna(z):
                    if z < -2:
                        results['ZScore'] = 'Buy'
                    elif z > 2:
                        results['ZScore'] = 'Sell'
                    else:
                        results['ZScore'] = 'Hold'
                else:
                    results['ZScore'] = 'Hold'
            else:
                results['ZScore'] = 'Hold'
        except Exception as e:
            results['ZScore'] = 'Error'
            print(f"ZScore Strategy Error: {e}")

        # ===== Strategy 10: Multi Ichimoku =====
        try:
            if len(df) >= 156:
                df['SpanA1'], df['SpanB1'] = ichimoku_custom(df, 9, 26, 52)
                df['SpanA2'], df['SpanB2'] = ichimoku_custom(df, 27, 78, 156)

                current = df.iloc[-1]
                span_a1 = current['SpanA1']
                span_b1 = current['SpanB1']
                span_a2 = current['SpanA2']
                span_b2 = current['SpanB2']
                
                if pd.notna(span_a1) and pd.notna(span_b1) and pd.notna(span_a2) and pd.notna(span_b2):
                    buy = (span_a1 > span_b1) or (span_a2 > span_b2)
                    sell = (span_a1 < span_b1) or (span_a2 < span_b2)
                    if buy and not sell:
                        results['Multi_Ichimoku'] = 'Buy'
                    elif sell and not buy:
                        results['Multi_Ichimoku'] = 'Sell'
                    else:
                        results['Multi_Ichimoku'] = 'Hold'
                else:
                    results['Multi_Ichimoku'] = 'Hold'
            else:
                results['Multi_Ichimoku'] = 'Hold'
        except Exception as e:
            results['Multi_Ichimoku'] = 'Error'
            print(f"Multi_Ichimoku Strategy Error: {e}")

        # ===== Strategy 11: Ehlers DFT-Adapted RSI =====
        try:
            df['DFT_RSI'] = ehlers_dft_adapted_rsi(df)
            if 'DFT_RSI' in df.columns and len(df) >= 89:
                fast = df['DFT_RSI'].ewm(span=13).mean()
                slow = df['DFT_RSI'].ewm(span=89).mean()
                if len(fast) >= 2 and len(slow) >= 2:
                    if pd.notna(fast.iloc[-1]) and pd.notna(slow.iloc[-1]) and pd.notna(fast.iloc[-2]) and pd.notna(slow.iloc[-2]):
                        if fast.iloc[-1] > slow.iloc[-1] and fast.iloc[-2] <= slow.iloc[-2]:
                            results['DFT_RSI'] = 'Buy'
                        elif fast.iloc[-1] < slow.iloc[-1] and fast.iloc[-2] >= slow.iloc[-2]:
                            results['DFT_RSI'] = 'Sell'
                        else:
                            results['DFT_RSI'] = 'Hold'
                    else:
                        results['DFT_RSI'] = 'Hold'
                else:
                    results['DFT_RSI'] = 'Hold'
            else:
                results['DFT_RSI'] = 'Hold'
        except Exception as e:
            results['DFT_RSI'] = 'Error'
            print(f"DFT_RSI Strategy Error: {e}")

        # ===== Strategy 12: PVT Cross =====
        try:
            results['PVT_Cross'] = pvt_cross_strategy(df)
        except Exception as e:
            results['PVT_Cross'] = 'Error'
            print(f"PVT_Cross Strategy Error: {e}")

        return results

    except Exception as e:
        print(f"Critical error in analyze_all_strategies: {e}")
        return {"Error": f"Critical error: {e}"}

# 사용 예시 함수
def get_strategy_summary(results):
    """전략 결과 요약"""
    if not results or "Error" in results:
        return "Analysis failed"
    
    buy_count = sum(1 for v in results.values() if v in ['Buy', 'StrongBuy'])
    sell_count = sum(1 for v in results.values() if v == 'Sell')
    hold_count = sum(1 for v in results.values() if v == 'Hold')
    
    total = len(results)
    
    return {
        'total_strategies': total,
        'buy_signals': buy_count,
        'sell_signals': sell_count,
        'hold_signals': hold_count,
        'buy_percentage': round(buy_count / total * 100, 1) if total > 0 else 0,
        'sell_percentage': round(sell_count / total * 100, 1) if total > 0 else 0,
        'overall_sentiment': 'Bullish' if buy_count > sell_count else 'Bearish' if sell_count > buy_count else 'Neutral'
    }